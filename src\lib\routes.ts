// Route configuration and utilities for authentication

export const AUTH_ROUTES = [
  '/sign-in',
  '/sign-up',
  '/forgot-password',
  '/reset-password',
  '/auth/callback'
];

export const PROTECTED_ROUTES = [
  '/profile',
  '/dashboard',
  '/settings',
  '/account'
];

export const PUBLIC_ROUTES = [
  '/',
  '/software',
  '/hardware',
  '/supported-models',
  '/resellers',
  '/payment-methods',
  '/contact',
  '/pricing',
  '/whats-new',
  '/knowledge-base',
  '/help-center',
  '/faq',
  '/terms-of-service',
  '/privacy-policy'
];

/**
 * Check if a route requires authentication
 */
export const isProtectedRoute = (pathname: string): boolean => {
  return PROTECTED_ROUTES.some(route => pathname.startsWith(route));
};

/**
 * Check if a route is an authentication route
 */
export const isAuthRoute = (pathname: string): boolean => {
  return AUTH_ROUTES.includes(pathname);
};

/**
 * Check if a route is public (doesn't require authentication)
 */
export const isPublicRoute = (pathname: string): boolean => {
  return PUBLIC_ROUTES.includes(pathname) || pathname === '/';
};

/**
 * Get the default redirect path after authentication
 */
export const getDefaultRedirectPath = (): string => {
  return '/';
};

/**
 * Validate if a redirect path is safe
 */
export const isSafeRedirectPath = (path: string): boolean => {
  // Prevent redirecting to external URLs or auth pages
  if (path.startsWith('http') || path.startsWith('//')) {
    return false;
  }
  
  // Don't redirect to auth pages
  if (isAuthRoute(path)) {
    return false;
  }
  
  return true;
};

/**
 * Get a safe redirect path, fallback to default if unsafe
 */
export const getSafeRedirectPath = (path: string | null): string => {
  if (!path || !isSafeRedirectPath(path)) {
    return getDefaultRedirectPath();
  }
  return path;
};
