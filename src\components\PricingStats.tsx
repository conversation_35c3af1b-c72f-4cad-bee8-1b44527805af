import React from 'react';
import { motion } from 'framer-motion';
import { TrendingUp, Users, Clock, Shield, Zap, Award } from 'lucide-react';
import { cn } from '@/lib/utils';

interface PricingStatsProps {
  theme?: 'software' | 'hardware';
}

const PricingStats: React.FC<PricingStatsProps> = ({ theme = 'software' }) => {
  const getThemeColors = () => {
    if (theme === 'hardware') {
      return {
        primary: 'text-pegasus-blue-600 dark:text-pegasus-blue-400',
        gradient: 'from-pegasus-blue-600 to-pegasus-blue-400',
        accent: 'pegasus-blue'
      };
    }
    return {
      primary: 'text-pegasus-orange',
      gradient: 'from-orange-600 to-orange-400',
      accent: 'orange'
    };
  };

  const themeColors = getThemeColors();

  const stats = [
    {
      icon: Users,
      value: '10,000+',
      label: 'Active Users',
      description: 'Technicians trust our tools',
      color: 'text-blue-600 dark:text-blue-400'
    },
    {
      icon: TrendingUp,
      value: '500K+',
      label: 'Successful Repairs',
      description: 'Operations completed',
      color: 'text-green-600 dark:text-green-400'
    },
    {
      icon: Clock,
      value: '99.9%',
      label: 'Uptime',
      description: 'Reliable service',
      color: themeColors.primary
    },
    {
      icon: Shield,
      value: '24/7',
      label: 'Support',
      description: 'Always here to help',
      color: 'text-purple-600 dark:text-purple-400'
    },
    {
      icon: Zap,
      value: '< 2s',
      label: 'Response Time',
      description: 'Lightning fast',
      color: 'text-yellow-600 dark:text-yellow-400'
    },
    {
      icon: Award,
      value: '4.9/5',
      label: 'Customer Rating',
      description: 'Highly rated service',
      color: 'text-red-600 dark:text-red-400'
    }
  ];

  const benefits = [
    {
      title: 'No Setup Fees',
      description: 'Start using immediately without any hidden costs',
      icon: '🚀'
    },
    {
      title: 'Cancel Anytime',
      description: 'No long-term contracts or commitments',
      icon: '✨'
    },
    {
      title: 'Data Security',
      description: 'Enterprise-grade security for your data',
      icon: '🔒'
    },
    {
      title: 'Regular Updates',
      description: 'New features and improvements monthly',
      icon: '🔄'
    }
  ];

  return (
    <div className="py-16 bg-[#111111] text-gray-300">
      <div className="container mx-auto px-4">
        {/* Stats Section */}
        <div className="text-center mb-16">
          <h3 className="text-3xl font-bold text-white mb-4">
            Trusted by <span className={themeColors.primary}>Professionals</span>
          </h3>
          <p className="text-gray-400 max-w-2xl mx-auto mb-12">
            Join thousands of technicians who rely on our platform for their daily operations
          </p>

          <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-8">
            {stats.map((stat, index) => (
              <motion.div
                key={stat.label}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5, delay: index * 0.1 }}
                className="text-center"
              >
                <div className="mb-4">
                  <div className={cn(
                    "inline-flex items-center justify-center w-12 h-12 rounded-full mb-3",
                    `bg-${stat.color.split('-')[1]}-100 dark:bg-${stat.color.split('-')[1]}-900/30`
                  )}>
                    <stat.icon className={cn("h-6 w-6", stat.color)} />
                  </div>
                </div>
                <div className="text-2xl font-bold text-white mb-1">
                  {stat.value}
                </div>
                <div className="text-sm font-semibold text-gray-300 mb-1">
                  {stat.label}
                </div>
                <div className="text-xs text-gray-400">
                  {stat.description}
                </div>
              </motion.div>
            ))}
          </div>
        </div>

        {/* Benefits Section */}
        <div className="bg-[#1a1a1a] rounded-3xl p-8 md:p-12">
          <div className="text-center mb-12">
            <h4 className="text-2xl font-bold text-white mb-4">
              Why Choose Our <span className={themeColors.primary}>Platform?</span>
            </h4>
            <p className="text-gray-400 max-w-2xl mx-auto">
              We've designed our pricing to be transparent, flexible, and value-driven
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            {benefits.map((benefit, index) => (
              <motion.div
                key={benefit.title}
                initial={{ opacity: 0, scale: 0.9 }}
                animate={{ opacity: 1, scale: 1 }}
                transition={{ duration: 0.5, delay: index * 0.1 }}
                className="text-center"
              >
                <div className="text-4xl mb-4">{benefit.icon}</div>
                <h5 className="text-lg font-semibold text-white mb-2">
                  {benefit.title}
                </h5>
                <p className="text-gray-400 text-sm">
                  {benefit.description}
                </p>
              </motion.div>
            ))}
          </div>
        </div>

        {/* Value Proposition */}
        <div className="mt-16 text-center">
          <div className="bg-gradient-to-r from-gray-900 to-gray-800 dark:from-gray-800 dark:to-gray-700 rounded-3xl p-8 md:p-12 text-white">
            <h4 className="text-3xl font-bold mb-4">
              Start Your Journey <span className="text-orange-400">Today</span>
            </h4>
            <p className="text-gray-300 text-lg mb-8 max-w-3xl mx-auto">
              Choose the plan that fits your needs and join thousands of satisfied technicians. 
              All plans include our core features with no hidden fees.
            </p>
            
            <div className="flex flex-col sm:flex-row gap-4 justify-center items-center">
              <div className="flex items-center gap-2 text-green-400">
                <Shield className="h-5 w-5" />
                <span className="text-sm font-semibold">30-Day Money Back Guarantee</span>
              </div>
              <div className="flex items-center gap-2 text-blue-400">
                <Zap className="h-5 w-5" />
                <span className="text-sm font-semibold">Instant Activation</span>
              </div>
              <div className="flex items-center gap-2 text-purple-400">
                <Award className="h-5 w-5" />
                <span className="text-sm font-semibold">Premium Support</span>
              </div>
            </div>
          </div>
        </div>

        {/* Pricing Transparency */}
        <div className="mt-16 bg-[#1a1a1a] rounded-2xl p-8">
          <div className="text-center">
            <h4 className="text-2xl font-bold text-white mb-4">
              💡 Pricing Transparency
            </h4>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6 text-left">
              <div className="bg-[#222222] rounded-xl p-6">
                <h5 className="font-semibold text-white mb-2">
                  ✅ What's Included
                </h5>
                <ul className="text-sm text-gray-400 space-y-1">
                  <li>• All listed features</li>
                  <li>• Regular updates</li>
                  <li>• Email support</li>
                  <li>• Data backup</li>
                </ul>
              </div>
              <div className="bg-[#222222] rounded-xl p-6">
                <h5 className="font-semibold text-white mb-2">
                  ❌ No Hidden Fees
                </h5>
                <ul className="text-sm text-gray-400 space-y-1">
                  <li>• No setup costs</li>
                  <li>• No cancellation fees</li>
                  <li>• No per-user charges</li>
                  <li>• No data export fees</li>
                </ul>
              </div>
              <div className="bg-[#222222] rounded-xl p-6">
                <h5 className="font-semibold text-white mb-2">
                  🔄 Flexible Terms
                </h5>
                <ul className="text-sm text-gray-400 space-y-1">
                  <li>• Monthly or yearly billing</li>
                  <li>• Upgrade/downgrade anytime</li>
                  <li>• Pause subscription option</li>
                  <li>• Pro-rated refunds</li>
                </ul>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default PricingStats;
