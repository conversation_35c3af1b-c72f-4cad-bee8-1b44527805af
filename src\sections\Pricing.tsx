
import React, { useState, useEffect } from 'react';
import { Button } from "@/components/ui/button";
import { Loader2 } from "lucide-react";
import { supabase } from "@/integrations/supabase/client";
import { useToast } from "@/components/ui/use-toast";
import { toast } from "sonner";
import { motion } from "framer-motion";
import PricingCard from '@/components/PricingCard';
import PlanComparison from '@/components/PlanComparison';
import PricingTestimonials from '@/components/PricingTestimonials';
import PricingFAQ from '@/components/PricingFAQ';
import AdvancedOfferBanner from '@/components/AdvancedOfferBanner';
import PricingStats from '@/components/PricingStats';

interface PricingPlan {
  id: string;
  name_plan: string;
  price: string;
  features: string;
  perks: string | null;
  validity_period?: string;
  unlock_limit?: number;
  readcode_limit?: number;
  other_operations_limit?: string;
  ai_access?: string;
}

interface Offer {
  id: string;
  percentage: string | null;
  expiry_at: string | null;
  status: string | null;
  title?: string;
  description?: string;
  badge_text?: string;
  urgency_level?: 'low' | 'medium' | 'high';
}

interface AdvancedOffer {
  id: string;
  title: string;
  description: string;
  discount_type: 'percentage' | 'fixed' | 'bogo' | 'tiered';
  discount_value: number;
  applicable_plans: string[];
  end_date: string;
  urgency_level: 'low' | 'medium' | 'high';
  badge_text: string;
  promo_code?: string;
  is_active: boolean;
}

interface PricingProps {
  theme?: 'software' | 'hardware';
}

const Pricing: React.FC<PricingProps> = ({ theme = 'software' }) => {
  const { toast: toastNotify } = useToast();
  const [plans, setPlans] = useState<PricingPlan[]>([]);
  const [activeOffer, setActiveOffer] = useState<Offer | null>(null);
  const [advancedOffer, setAdvancedOffer] = useState<AdvancedOffer | null>(null);
  const [appliedPromoCode, setAppliedPromoCode] = useState<string | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const fetchPricingPlans = async () => {
      try {
        // Fetch pricing plans
        const { data: plansData, error: plansError } = await supabase
          .from('pricing')
          .select('*')
          .order('price');

        if (plansError) throw plansError;

        // Fetch active offers
        const now = new Date().toISOString();
        const { data: offersData, error: offersError } = await supabase
          .from('offers')
          .select('*')
          .eq('status', 'Plans')
          .gt('expiry_at', now)
          .order('created_at', { ascending: false })
          .limit(1);

        if (offersError) throw offersError;

        // Set the active offer if available
        if (offersData && offersData.length > 0) {
          setActiveOffer(offersData[0]);
        }

        // Fetch advanced offers
        const { data: advancedOffersData, error: advancedOffersError } = await supabase
          .from('advanced_offers')
          .select('*')
          .eq('is_active', true)
          .gt('end_date', now)
          .order('urgency_level', { ascending: false })
          .order('created_at', { ascending: false })
          .limit(1);

        if (advancedOffersError) {
          console.warn('Advanced offers not available:', advancedOffersError);
        } else if (advancedOffersData && advancedOffersData.length > 0) {
          setAdvancedOffer(advancedOffersData[0]);
        }

        setPlans(plansData || []);
      } catch (error) {
        toastNotify({
          title: "Error",
          description: "Failed to fetch pricing information. Please try again later.",
          variant: "destructive"
        });
      } finally {
        setLoading(false);
      }
    };

    fetchPricingPlans();
  }, [toastNotify]);

  // Function to generate features from plan data
  const generateFeatures = (plan: PricingPlan): string[] => {
    const features: string[] = [];

    // Add unlock limit feature
    if (plan.unlock_limit) {
      features.push(`${plan.unlock_limit} Unlock Operations`);
    }

    // Add read code feature
    if (plan.readcode_limit && plan.readcode_limit > 0) {
      features.push(`${plan.readcode_limit} Read Code Operations`);
    }

    // Add other operations feature
    if (plan.other_operations_limit) {
      const limit = plan.other_operations_limit === 'Unlimited' ? 'Unlimited' : plan.other_operations_limit;
      features.push(`${limit} Other Operations`);
    }

    // Add AI access feature
    if (plan.ai_access) {
      features.push(`${plan.ai_access} AI Assistant Access`);
    }

    // Add validity period
    if (plan.validity_period) {
      features.push(`Valid ${plan.validity_period}`);
    }

    // Parse additional features if available
    if (plan.features) {
      try {
        const additionalFeatures = plan.features.split('\n').map(feature => feature.trim()).filter(Boolean);
        features.push(...additionalFeatures);
      } catch (e) {
        // Ignore parsing errors
      }
    }

    return features;
  };

  // Function to parse perks string into an array
  const parsePerks = (perksStr: string | null): string[] => {
    if (!perksStr) return [];
    try {
      return perksStr.split('\n').map(perk => perk.trim()).filter(Boolean);
    } catch (e) {
      return [];
    }
  };

  // Enhanced function to calculate discounted price with advanced offers support
  const calculateDiscountedPrice = (originalPrice: string, planName: string): {
    original: string;
    discounted: string | null;
    savings: string | null;
    discountPercentage: string | null;
    offerType: string | null;
  } => {
    const price = parseFloat(originalPrice);
    let bestDiscount = { amount: 0, percentage: 0, type: '', savings: 0 };

    // Check legacy offer
    if (activeOffer && activeOffer.percentage) {
      const discountStr = activeOffer.percentage.replace('%', '');
      const discountPercentage = parseFloat(discountStr);
      if (!isNaN(discountPercentage)) {
        const discountAmount = price * (discountPercentage / 100);
        if (discountAmount > bestDiscount.amount) {
          bestDiscount = {
            amount: discountAmount,
            percentage: discountPercentage,
            type: 'Legacy Offer',
            savings: discountAmount
          };
        }
      }
    }

    // Check advanced offer
    if (advancedOffer && advancedOffer.applicable_plans.includes(planName)) {
      let discountAmount = 0;
      let discountPercentage = 0;

      switch (advancedOffer.discount_type) {
        case 'percentage':
          discountPercentage = advancedOffer.discount_value;
          discountAmount = price * (discountPercentage / 100);
          break;
        case 'fixed':
          discountAmount = Math.min(advancedOffer.discount_value, price);
          discountPercentage = (discountAmount / price) * 100;
          break;
        case 'tiered':
          // Simple tiered logic - could be enhanced
          if (price >= 100) discountPercentage = advancedOffer.discount_value;
          else if (price >= 50) discountPercentage = advancedOffer.discount_value * 0.75;
          else discountPercentage = advancedOffer.discount_value * 0.5;
          discountAmount = price * (discountPercentage / 100);
          break;
      }

      if (discountAmount > bestDiscount.amount) {
        bestDiscount = {
          amount: discountAmount,
          percentage: discountPercentage,
          type: advancedOffer.title,
          savings: discountAmount
        };
      }
    }

    // Apply promo code discount if applicable
    if (appliedPromoCode && advancedOffer && advancedOffer.promo_code === appliedPromoCode) {
      // Promo code already included in advanced offer calculation
    }

    if (bestDiscount.amount === 0) {
      return { original: originalPrice, discounted: null, savings: null, discountPercentage: null, offerType: null };
    }

    const discountedPrice = price - bestDiscount.amount;

    return {
      original: originalPrice,
      discounted: discountedPrice.toFixed(2),
      savings: bestDiscount.savings.toFixed(2),
      discountPercentage: `${Math.round(bestDiscount.percentage)}%`,
      offerType: bestDiscount.type
    };
  };

  const handleChoosePlan = (plan: PricingPlan) => {
    toast(`You've selected the ${plan.name_plan} plan`, {
      description: "Contact sales for next steps.",
      action: {
        label: "Contact Sales",
        onClick: () => {
          const contactSection = document.getElementById('contact');
          if (contactSection) {
            contactSection.scrollIntoView({ behavior: 'smooth' });
          }
        }
      }
    });
  };

  const handleApplyPromoCode = async (code: string) => {
    try {
      // Verify promo code exists and is valid
      const { data: promoData, error: promoError } = await supabase
        .from('advanced_offers')
        .select('*')
        .eq('promo_code', code.toUpperCase())
        .eq('is_active', true)
        .gt('end_date', new Date().toISOString())
        .single();

      if (promoError || !promoData) {
        toast('Invalid or expired promo code', {
          description: 'Please check your code and try again.',
        });
        return;
      }

      setAppliedPromoCode(code.toUpperCase());
      toast('Promo code applied successfully!', {
        description: `You'll get ${promoData.discount_type === 'percentage' ? promoData.discount_value + '%' : '$' + promoData.discount_value} off`,
      });
    } catch (error) {
      toast('Error applying promo code', {
        description: 'Please try again later.',
      });
    }
  };

  // Function to get theme-aware colors
  const getThemeColors = () => {
    if (theme === 'hardware') {
      return {
        plus: 'text-pegasus-blue-600 dark:text-pegasus-blue-400',
        gradient: 'from-pegasus-blue-600 to-pegasus-blue-400',
        loader: 'text-pegasus-blue-600'
      };
    }
    return {
      plus: 'text-pegasus-orange',
      gradient: 'from-orange-600 to-orange-400',
      loader: 'text-pegasus-orange'
    };
  };

  // Function to determine the plan variant based on theme
  const getPlanVariant = (planName: string): 'plus' | 'pro' | 'max' | 'basic' => {
    const lowerPlanName = planName.toLowerCase();
    if (lowerPlanName.includes('max')) return 'max';
    if (lowerPlanName.includes('standard')) return 'pro';
    if (lowerPlanName.includes('basic')) return theme === 'hardware' ? 'basic' : 'basic';
    return theme === 'hardware' ? 'basic' : 'plus';
  };

  // Enhanced function to determine if plan is recommended and get popularity info
  const getPlanPopularity = (planName: string, index: number): {
    isRecommended: boolean;
    isMostPopular: boolean;
    badge: string | null;
  } => {
    const lowerPlanName = planName.toLowerCase();

    // Most popular plans (typically mid-tier)
    const isMostPopular = lowerPlanName.includes('pro') || lowerPlanName.includes('plus');

    // Recommended plans (typically high-tier)
    const isRecommended = lowerPlanName.includes('max') && !lowerPlanName.includes('pro');

    // Determine badge text
    let badge = null;
    if (isMostPopular) badge = 'Most Popular';
    else if (isRecommended) badge = 'Best Value';
    else if (lowerPlanName.includes('basic')) badge = 'Starter';

    return { isRecommended, isMostPopular, badge };
  };

  const themeColors = getThemeColors();

  return (
    
    <div className="py-20 bg-[#111111] text-gray-300 relative overflow-hidden" id="pricing">
      <div className="absolute inset-0 z-1 pointer-events-none" style={{
        background: 'linear-gradient(to bottom, transparent 0%, #111111 90%), radial-gradient(ellipse at center, transparent 40%, #111111 95%)'
      }}></div>

      {/* Advanced Offer Banner */}
      {advancedOffer && (
        <div className="container mx-auto px-4 relative z-10">
          <AdvancedOfferBanner
            offer={advancedOffer}
            onApplyCode={handleApplyPromoCode}
            theme={theme}
          />
        </div>
      )}

      {/* Legacy Special Offer Banner */}
      {!advancedOffer && activeOffer && (
        <motion.div
          className="container mx-auto px-4 relative z-10 mb-8"
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
        >
          <div className="bg-gradient-to-r from-red-600 to-orange-600 rounded-2xl p-6 text-center text-white shadow-2xl border border-red-500/20">
            <div className="flex items-center justify-center gap-2 mb-2">
              <span className="animate-pulse text-2xl">🔥</span>
              <h3 className="text-xl font-bold">Limited Time Offer!</h3>
              <span className="animate-pulse text-2xl">🔥</span>
            </div>
            <p className="text-lg mb-2">
              Save <span className="font-bold text-2xl">{activeOffer.percentage}</span> on all plans
            </p>
            <p className="text-sm opacity-90">
              Offer expires: {activeOffer.expiry_at ? new Date(activeOffer.expiry_at).toLocaleDateString() : 'Soon'}
            </p>
          </div>
        </motion.div>
      )}

      {/* Header Section */}
      <div className="container mx-auto px-4 relative z-10 mb-16">
        <div className="text-center">
          <div className="mb-6">
            <span className={`bg-[#1a1a1a] border border-gray-700 px-4 py-1 rounded-full text-xs sm:text-sm font-medium ${theme === 'hardware' ? 'text-pegasus-blue-400' : 'text-orange-400'}`}>
             Pricing Plans
            </span>
          </div>
          <h2 className="text-3xl md:text-4xl lg:text-5xl font-semibold text-white leading-tight mb-4">
            Choose Your <span className={theme === 'hardware' ? 'text-pegasus-blue-400' : 'text-orange-400'}>Perfect Plan</span>
          </h2>
          <p className="text-base sm:text-lg lg:text-xl text-gray-400 max-w-2xl mx-auto">
            Professional pricing plans designed for technicians and repair businesses
            {activeOffer && (
              <span className="block mt-2 text-orange-400 font-semibold">
                🎉 All plans now {activeOffer.percentage} OFF!
              </span>
            )}
          </p>
        </div>
      </div>

      {/* Pricing Plans */}
      <div className="container mx-auto px-4 relative z-10">
        {loading ? (
          <div className="flex flex-col items-center justify-center min-h-[400px]">
            <Loader2 className={`h-12 w-12 ${theme === 'hardware' ? 'text-pegasus-blue-400' : 'text-orange-400'} animate-spin mb-4`} />
            <p className="text-lg text-gray-400">Loading pricing plans...</p>
          </div>
        ) : plans.length > 0 ? (
            <motion.div
              className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 max-w-7xl mx-auto"
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ duration: 0.6 }}
            >
              {plans.map((plan, index) => {
                const features = generateFeatures(plan);
                const perks = parsePerks(plan.perks);
                const planVariant = getPlanVariant(plan.name_plan);
                const popularity = getPlanPopularity(plan.name_plan, index);

                // Apply discount if offer is active
                const priceInfo = calculateDiscountedPrice(plan.price, plan.name_plan);

                return (
                  <PricingCard
                    key={plan.id}
                    id={plan.id}
                    name={plan.name_plan}
                    price={priceInfo.discounted || priceInfo.original}
                    originalPrice={priceInfo.discounted ? priceInfo.original : undefined}
                    features={features}
                    perks={perks}
                    index={index}
                    recommended={popularity.isRecommended}
                    mostPopular={popularity.isMostPopular}
                    badge={popularity.badge}
                    variant={planVariant}
                    savings={priceInfo.savings}
                    discountPercentage={priceInfo.discountPercentage}
                    validityPeriod={plan.validity_period}
                    offerType={priceInfo.offerType}
                    onChoosePlan={() => handleChoosePlan(plan)}
                  />
                );
              })}
            </motion.div>
          ) : (
            <div className="text-center py-20 px-4 max-w-2xl mx-auto">
              <h3 className="text-2xl font-bold text-white mb-4">No pricing plans available at the moment</h3>
              <p className="text-gray-400 mb-6">
                Please check back later or contact us for custom pricing tailored to your needs.
              </p>
              <Button
                onClick={() => {
                  const contactSection = document.getElementById('contact');
                  if (contactSection) {
                    contactSection.scrollIntoView({ behavior: 'smooth' });
                  }
                }}
                className={theme === 'hardware'
                  ? "bg-pegasus-blue hover:bg-pegasus-blue-700 text-white"
                  : "bg-pegasus-orange hover:bg-pegasus-orange-600 text-white"
                }
              >
                Contact Us
              </Button>
            </div>
          )}
        </div>

        {/* Additional Sections */}
        {plans.length > 0 && (
          <>
            <PricingStats theme={theme} />
            <PlanComparison theme={theme} />
            <PricingTestimonials theme={theme} />
            <PricingFAQ theme={theme} />
          </>
        )}
      </div>
  );
};

export default Pricing;
