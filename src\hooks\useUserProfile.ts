import { useState, useEffect } from 'react';
import { User } from '@supabase/supabase-js';
import { supabase } from '@/integrations/supabase/client';
import { toast } from 'sonner';

export interface UserProfileData {
  id: string;
  name: string | null;
  email: string;
  phone: string | null;
  country: string | null;
  credits: string | null;
  user_type: string | null;
  activate: string | null;
  block: string | null;
  expiry_time: string | null;
  start_date: string | null;
  hwid: string | null;
  two_factor_enabled: boolean | null;
  my_plans: string | null;
}

export interface UserStats {
  totalSessions: number;
  totalOperations: number;
  joinDate: string;
  lastLogin: string;
  accountStatus: 'active' | 'blocked' | 'expired';
  securityScore: number;
  profileViews: number;
  userRating: number;
}

export interface RecentActivity {
  operation_type: string;
  time: string;
  status: string;
  model?: string;
  brand?: string;
}

export const useUserProfile = (authUser: User | null) => {
  const [profileData, setProfileData] = useState<UserProfileData | null>(null);
  const [userStats, setUserStats] = useState<UserStats | null>(null);
  const [recentActivity, setRecentActivity] = useState<RecentActivity[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Fetch user profile data from the users table
  const fetchUserProfile = async (userId: string) => {
    try {
      const { data, error } = await supabase
        .from('users')
        .select('*')
        .eq('uid', userId)
        .single();

      if (error) {
        if (error.code === 'PGRST116') {
          // User not found in users table, create a new record
          await createUserProfile(userId);
          return;
        }
        throw error;
      }

      setProfileData(data);
    } catch (err) {
      console.error('Error fetching user profile:', err);
      setError('Failed to fetch user profile');
    }
  };

  // Create user profile in users table if it doesn't exist
  const createUserProfile = async (userId: string) => {
    if (!authUser) return;

    try {
      const newUserData = {
        uid: userId,
        id: crypto.randomUUID(),
        email: authUser.email || '',
        name: authUser.user_metadata?.full_name || authUser.user_metadata?.name || null,
        phone: authUser.user_metadata?.phone || null,
        country: authUser.user_metadata?.location || null,
        password: 'managed_by_supabase_auth', // This will be managed by Supabase Auth
        activate: 'yes',
        block: 'no',
        credits: '0',
        email_type: 'regular',
        expiry_time: null,
        start_date: new Date().toISOString().split('T')[0], // Format as YYYY-MM-DD
        hwid: null,
        two_factor_enabled: false,
        my_plans: null
      };

      const { data, error } = await supabase
        .from('users')
        .insert([newUserData])
        .select()
        .single();

      if (error) throw error;

      setProfileData(data);
      toast.success('Profile created successfully!');
    } catch (err) {
      console.error('Error creating user profile:', err);
      setError('Failed to create user profile');
      toast.error('Failed to create user profile');
    }
  };

  // Fetch user statistics and activity
  const fetchUserStats = async (userId: string) => {
    try {
      // Get operations count and recent activity
      const { data: operations, error: opsError } = await supabase
        .from('operations')
        .select('operation_type, time, status, model, brand')
        .eq('uid', userId)
        .order('time', { ascending: false });

      if (opsError) throw opsError;

      // Calculate stats
      const totalOperations = operations?.length || 0;
      const recentOps = operations?.slice(0, 5) || [];
      
      // Calculate security score based on account status and 2FA
      let securityScore = 70; // Base score
      if (profileData?.two_factor_enabled) securityScore += 20;
      if (profileData?.block === 'no') securityScore += 10;
      
      // Mock some additional stats that would typically come from analytics
      const profileViews = Math.floor(totalOperations * 2.5) + Math.floor(Math.random() * 100);
      const userRating = Math.min(5.0, 3.5 + (totalOperations * 0.01));

      const stats: UserStats = {
        totalSessions: totalOperations, // Using operations as sessions for now
        totalOperations,
        joinDate: authUser?.created_at ? new Date(authUser.created_at).toLocaleDateString() : '',
        lastLogin: authUser?.last_sign_in_at ? new Date(authUser.last_sign_in_at).toLocaleDateString() : '',
        accountStatus: profileData?.block === 'yes' ? 'blocked' : 
                      (profileData?.expiry_time && new Date(profileData.expiry_time) < new Date()) ? 'expired' : 'active',
        securityScore,
        profileViews,
        userRating: Math.round(userRating * 10) / 10
      };

      setUserStats(stats);
      setRecentActivity(recentOps.map(op => ({
        operation_type: op.operation_type,
        time: op.time,
        status: op.status,
        model: op.model,
        brand: op.brand
      })));

    } catch (err) {
      console.error('Error fetching user stats:', err);
      setError('Failed to fetch user statistics');
    }
  };

  // Update user profile
  const updateUserProfile = async (updates: Partial<UserProfileData>) => {
    if (!authUser || !profileData) return { error: 'No user data available' };

    try {
      const { data, error } = await supabase
        .from('users')
        .update(updates)
        .eq('uid', authUser.id)
        .select()
        .single();

      if (error) throw error;

      setProfileData(data);
      
      // Also update auth user metadata if relevant fields are updated
      const authUpdates: any = {};
      if (updates.name) authUpdates.full_name = updates.name;
      if (updates.phone) authUpdates.phone = updates.phone;
      if (updates.country) authUpdates.location = updates.country;

      if (Object.keys(authUpdates).length > 0) {
        await supabase.auth.updateUser({
          data: authUpdates
        });
      }

      return { error: null };
    } catch (err) {
      console.error('Error updating user profile:', err);
      return { error: 'Failed to update profile' };
    }
  };

  // Main effect to fetch data when user changes
  useEffect(() => {
    const fetchData = async () => {
      if (!authUser) {
        setLoading(false);
        return;
      }

      setLoading(true);
      setError(null);

      try {
        await fetchUserProfile(authUser.id);
      } catch (err) {
        console.error('Error in fetchData:', err);
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, [authUser]);

  // Fetch stats when profile data is available
  useEffect(() => {
    if (profileData && authUser) {
      fetchUserStats(authUser.id);
    }
  }, [profileData, authUser]);

  return {
    profileData,
    userStats,
    recentActivity,
    loading,
    error,
    updateUserProfile,
    refetch: () => {
      if (authUser) {
        fetchUserProfile(authUser.id);
      }
    }
  };
};
