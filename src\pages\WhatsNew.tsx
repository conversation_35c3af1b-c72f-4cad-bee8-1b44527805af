
import React, { useState, useEffect } from 'react';
import { supabase } from "@/integrations/supabase/client";
import { Loader2, Calendar, Download, Info, ArrowUp, ArrowDown, Code, CheckCircle, Star, Tag } from "lucide-react";
import { toast } from "sonner";
import { But<PERSON> } from "@/components/ui/button";
import { motion, AnimatePresence } from "framer-motion";
import { useToast } from "@/components/ui/use-toast";

/**
 * @typedef {Object} UpdateItem
 * @property {string} varizon
 * @property {string|null} name
 * @property {string|null} changelog
 * @property {string|null} release_at
 * @property {string|null} link
 */

const WhatsNew = () => {
  const { toast: notifyToast } = useToast();
  const [updates, setUpdates] = useState<UpdateItem>([]);
  const [loading, setLoading] = useState(true);
  const [latestVersion, setLatestVersion] = useState<string | null>(null);
  const [expanded, setExpanded] = useState<string | null>(null);

  useEffect(() => {
    const fetchUpdates = async () => {
      try {
        const { data, error } = await supabase
          .from('update')
          .select('varizon, name, changelog, release_at, link')
          .order('release_at', { ascending: false });

        if (error) throw error;
        setUpdates(data || []);

        // Set the latest version for comparison
        if (data && data.length > 0) {
          setLatestVersion(data[0].varizon);
          // Automatically expand the latest version
          setExpanded(data[0].varizon);
        }
      } catch (error) {
        toast.error('Failed to load update history');
      } finally {
        setLoading(false);
      }
    };

    fetchUpdates();
  }, []);

  const formatDate = (dateString) => {
    if (!dateString) return 'Unknown date';
    const date = new Date(dateString);
    return new Intl.DateTimeFormat('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    }).format(date);
  };

  const handleDownload = async (link, version) => {
    if (!link) {
      toast.error('Download link not available');
      return;
    }

    try {
      // Call the increment_counter function
      const { data, error } = await supabase.rpc('increment_counter');

      if (error) {
        toast.error('Failed to process download');
      } else {

        // Open the download link in a new tab
        window.open(link, '_blank');
        toast.success('Download started');
      }
    } catch (error) {
      toast.error('Failed to process download');
    }
  };

  const toggleExpand = (version) => {
    if (expanded === version) {
      setExpanded(null);
    } else {
      setExpanded(version);
    }
  };

  // Get icon based on version number
  const getUpdateIcon = (index) => {
    const icons = [Star, CheckCircle, Code, Tag];
    return icons[index % icons.length];
  };

  // Generate random highlight color classes for the timeline nodes
  const getHighlightColor = (index) => {
    const colors = [
      'bg-purple-500',
      'bg-purple-600',
      'bg-purple-400',
      'bg-blue-500',
      'bg-indigo-500',
    ];
    return colors[index % colors.length];
  };

  // Animation variants for timeline items
  const timelineItemVariants = {
    hidden: { opacity: 0, x: -50 },
    show: {
      opacity: 1,
      x: 0,
      transition: {
        duration: 0.7,
        ease: [0.22, 1, 0.36, 1]
      }
    }
  };

  return (
    <div className="min-h-screen bg-[#111111] text-gray-300 pt-20 pb-16">
      {/* Background Effects */}
      <div className="absolute inset-0 z-0 pointer-events-none" style={{
        background: 'linear-gradient(to bottom, transparent 0%, #111111 90%), radial-gradient(ellipse at center, transparent 40%, #111111 95%)'
      }}></div>

      {/* Animated particles */}
      {[...Array(8)].map((_, i) => (
        <motion.div
          key={i}
          className="absolute rounded-full bg-purple-400/10"
          style={{
            width: Math.random() * 30 + 10,
            height: Math.random() * 30 + 10,
            top: `${Math.random() * 100}%`,
            left: `${Math.random() * 100}%`,
          }}
          animate={{
            y: [0, -80, 0],
            x: [0, Math.random() * 40 - 20, 0],
            opacity: [0, 0.3, 0],
          }}
          transition={{
            duration: Math.random() * 15 + 10,
            repeat: Infinity,
            delay: Math.random() * 5,
          }}
        />
      ))}

      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
        <motion.div
          className="text-center mb-16"
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
        >
          <div className="mb-6">
            <span className="bg-[#1a1a1a] border border-gray-700 text-purple-400 px-4 py-1 rounded-full text-xs sm:text-sm font-medium">
              Latest Updates
            </span>
          </div>
          <h1 className="text-3xl md:text-4xl lg:text-5xl font-semibold text-white leading-tight mb-4">
            What's New in <span className="text-purple-400">Pegasus Tool</span>
          </h1>
          <p className="text-base sm:text-lg lg:text-xl text-gray-400 max-w-2xl mx-auto">
            Track our software updates and new features as we continue to evolve
          </p>
        </motion.div>

        {loading ? (
          <div className="flex justify-center items-center py-24">
            <Loader2 className="h-12 w-12 text-purple-400 animate-spin" />
          </div>
        ) : updates.length > 0 ? (
          <div className="relative">
            {/* Timeline line */}
            <div className="absolute left-0 md:left-8 top-0 bottom-0 w-1 bg-gradient-to-b from-purple-400 via-purple-500 to-purple-600"></div>

            <motion.div
              className="space-y-12"
              initial="hidden"
              animate="show"
              variants={{
                hidden: {},
                show: {
                  transition: {
                    staggerChildren: 0.15
                  }
                }
              }}
            >
              {updates.map((update, index) => {
                const isLatest = update.varizon === latestVersion;
                const isExpanded = expanded === update.varizon;

                return (
                  <motion.div
                    key={update.varizon}
                    className="relative pl-14 md:pl-24"
                    variants={timelineItemVariants}
                  >

                    {/* Content card */}
                    <motion.div
                      className={`bg-[#1a1a1a] border border-gray-700/50 rounded-xl overflow-hidden backdrop-blur-lg hover:border-purple-400/50 transition-all duration-300 hover:-translate-y-2 ${isLatest ? 'border-l-4 border-purple-400' : ''}`}
                      whileHover={{ y: -5 }}
                      layout
                    >
                      {/* Header */}
                      <div className="p-6 cursor-pointer" onClick={() => toggleExpand(update.varizon)}>
                        <div className="flex justify-between items-start">
                          <div className="flex-1">
                            <h3 className="text-2xl font-bold text-white flex items-center gap-2">
                              {update.name || `Version ${update.varizon}`}
                              {isLatest && (
                                <span className="text-xs font-normal bg-purple-400/20 text-purple-400 px-2 py-0.5 rounded-full">
                                  Latest
                                </span>
                              )}
                            </h3>
                            <div className="flex items-center text-sm text-gray-400 mt-2">
                              <Calendar className="h-4 w-4 mr-2" />
                              {formatDate(update.release_at)}
                            </div>
                          </div>
                          <button
                            className={`p-2 rounded-full transition-colors ${isExpanded ? 'bg-purple-400/10 text-purple-400' : 'bg-gray-700/50 text-gray-400 hover:text-purple-400'}`}
                          >
                            {isExpanded ? <ArrowUp className="h-5 w-5" /> : <ArrowDown className="h-5 w-5" />}
                          </button>
                        </div>
                      </div>

                      {/* Changelog content - animated expansion */}
                      <AnimatePresence>
                        {isExpanded && (
                          <motion.div
                            initial={{ opacity: 0, height: 0 }}
                            animate={{ opacity: 1, height: "auto" }}
                            exit={{ opacity: 0, height: 0 }}
                            transition={{ duration: 0.3, ease: "easeInOut" }}
                          >
                            <div className="px-6 pb-4 border-t border-gray-700/50">
                              <div className="pt-4 text-gray-300 prose prose-invert max-w-none">
                                {update.changelog ? (
                                  update.changelog.split('\n').map((line, i) => {
                                    // Check if line is a heading (starts with # or ##)
                                    if (line.startsWith('# ')) {
                                      return <h3 key={i} className="text-xl font-semibold mt-3 mb-2 text-white">{line.replace('# ', '')}</h3>;
                                    } else if (line.startsWith('## ')) {
                                      return <h4 key={i} className="text-lg font-semibold mt-3 mb-2 text-white">{line.replace('## ', '')}</h4>;
                                    } else if (line.startsWith('- ')) {
                                      // Check if line is a list item (starts with -)
                                      return (
                                        <div key={i} className="flex items-start space-x-2 my-1.5">
                                          <span className="h-1.5 w-1.5 rounded-full bg-purple-400 mt-2"></span>
                                          <span>{line.replace('- ', '')}</span>
                                        </div>
                                      );
                                    } else if (line === '') {
                                      return <div key={i} className="h-2"></div>; // Empty line spacer
                                    } else {
                                      return <p key={i} className="my-2">{line}</p>;
                                    }
                                  })
                                ) : (
                                  <p className="italic text-gray-400">No changelog available for this update.</p>
                                )}
                              </div>
                            </div>

                            {/* Only show the download button for the latest version */}
                            {isLatest && update.link && (
                              <div className="flex justify-end items-center p-4 bg-gray-800/50 border-t border-gray-700/50">
                                <Button
                                  onClick={() => handleDownload(update.link, update.varizon)}
                                  className="bg-purple-600 hover:bg-purple-700 text-white flex items-center gap-2 shadow-md hover:shadow-lg transform hover:-translate-y-1 transition-all duration-300"
                                >
                                  <Download className="h-4 w-4" />
                                  Download v{update.varizon}
                                </Button>
                              </div>
                            )}
                          </motion.div>
                        )}
                      </AnimatePresence>
                    </motion.div>
                  </motion.div>
                );
              })}
            </motion.div>
          </div>
        ) : (
          <motion.div
            className="text-center py-16 bg-[#1a1a1a] border border-gray-700/50 rounded-xl backdrop-blur-lg"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5 }}
          >
            <h3 className="text-xl font-medium text-white mb-2">No updates available</h3>
            <p className="text-gray-400">Check back later for updates on Pegasus Tool</p>
          </motion.div>
        )}
      </div>
    </div>
  );
};

export default WhatsNew;
