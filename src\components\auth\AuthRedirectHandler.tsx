import { useAuth } from "@/contexts/AuthContext";
import { useEffect } from "react";
import { useNavigate } from "react-router-dom";
import { getSafeRedirectPath } from "@/lib/routes";

export const AuthRedirectHandler = () => {
  const { user, redirectAfterAuth, clearRedirect } = useAuth();
  const navigate = useNavigate();

  useEffect(() => {
    // If user is authenticated and there's a redirect path, navigate to it
    if (user && redirectAfterAuth) {
      // Small delay to ensure the auth state is fully settled
      const timer = setTimeout(() => {
        const safePath = getSafeRedirectPath(redirectAfterAuth);
        navigate(safePath, { replace: true });
        clearRedirect();
      }, 100);

      return () => clearTimeout(timer);
    }
  }, [user, redirectAfterAuth, navigate, clearRedirect]);

  return null; // This component doesn't render anything
};

export default AuthRedirectHandler;
