import React from 'react';
import { Link } from 'react-router-dom';
import { motion } from 'framer-motion';
import { cn } from '@/lib/utils';
import { usePageColors } from '@/hooks/usePageColors';
import {
  FacebookIcon, TwitterIcon, GithubIcon, InstagramIcon, LinkedinIcon,
  Mail, Phone, MapPin, Globe, Heart, Zap, Shield,
  Download, Users, Star, TrendingUp, ChevronRight,
  FlashlightOffIcon,
  Package
} from 'lucide-react';

const Footer = () => {
  const colors = usePageColors();

  // Animation variants
  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        duration: 0.6,
        staggerChildren: 0.1
      }
    }
  };

  const itemVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: {
      opacity: 1,
      y: 0,
      transition: { duration: 0.5 }
    }
  };

  const socialLinks = [
    { icon: FacebookIcon, href: "https://facebook.com", label: "Facebook", color: "hover:text-blue-400" },
    { icon: TwitterIcon, href: "https://twitter.com", label: "Twitter", color: "hover:text-sky-400" },
    { icon: InstagramIcon, href: "https://instagram.com", label: "Instagram", color: "hover:text-pink-400" },
    { icon: LinkedinIcon, href: "https://linkedin.com", label: "LinkedIn", color: "hover:text-blue-500" },
    { icon: GithubIcon, href: "https://github.com", label: "GitHub", color: "hover:text-gray-300" },
  ];

  const stats = [
    { icon: Download, value: "50K+", label: "Downloads", color: "text-blue-400" },
    { icon: Users, value: "10K+", label: "Active Users", color: "text-green-400" },
    { icon: Star, value: "4.9", label: "Rating", color: "text-yellow-400" },
    { icon: Globe, value: "120+", label: "Countries", color: "text-purple-400" },
  ];

  return (
    <footer className="bg-[#111111] text-gray-300 relative overflow-hidden">
      {/* Background Effects */}
      <div className="absolute inset-0 z-0 pointer-events-none" style={{
        background: 'linear-gradient(to top, transparent 0%, #111111 90%), radial-gradient(ellipse at center, transparent 40%, #111111 95%)'
      }}></div>

      {/* Animated particles */}
      {[...Array(12)].map((_, i) => (
        <motion.div
          key={i}
          className="absolute rounded-full bg-purple-500/5"
          style={{
            width: Math.random() * 80 + 30,
            height: Math.random() * 80 + 30,
            top: `${Math.random() * 100}%`,
            left: `${Math.random() * 100}%`,
          }}
          animate={{
            y: [0, -150, 0],
            x: [0, Math.random() * 80 - 40, 0],
            opacity: [0, 0.3, 0],
          }}
          transition={{
            duration: Math.random() * 25 + 20,
            repeat: Infinity,
            delay: Math.random() * 15,
          }}
        />
      ))}

      <div className="relative z-10">
        {/* Stats Section */}
        <motion.div
          initial="hidden"
          whileInView="visible"
          viewport={{ once: true }}
          variants={containerVariants}
          className="border-b border-gray-800/50 py-16"
        >
          <div className="container mx-auto px-4">
            <motion.div variants={itemVariants} className="text-center mb-12">
              <div className="mb-6">
                <span className={`bg-[#1a1a1a] border border-gray-700 ${colors.text} px-4 py-1 rounded-full text-sm font-medium`}>
                  Our Impact
                </span>
              </div>
              <h2 className="text-3xl md:text-4xl font-bold text-white mb-4">
                Trusted by professionals <span className={colors.text}>worldwide</span>
              </h2>
              <p className="text-gray-400 max-w-2xl mx-auto">
                Join thousands of repair professionals who trust Pegasus Tool for their smartphone unlocking needs
              </p>
            </motion.div>

            <div className="grid grid-cols-2 md:grid-cols-4 gap-8">
              {stats.map((stat, index) => {
                const Icon = stat.icon;
                return (
                  <motion.div
                    key={index}
                    variants={itemVariants}
                    className="text-center"
                  >
                    <div className={`bg-[#1a1a1a] border border-gray-700/50 rounded-xl p-6 backdrop-blur-lg transition-all duration-300 hover:-translate-y-2`}
                         style={{
                           borderColor: `${colors.accent}20`,
                         }}
                         onMouseEnter={(e) => {
                           e.currentTarget.style.borderColor = `${colors.accent}50`;
                         }}
                         onMouseLeave={(e) => {
                           e.currentTarget.style.borderColor = `${colors.accent}20`;
                         }}>
                      <div className="w-12 h-12 flex items-center justify-center rounded-full mb-4 mx-auto"
                           style={{
                             background: `linear-gradient(135deg, ${colors.accent}30, ${colors.accent}20)`
                           }}>
                        <Icon className={`h-6 w-6 ${stat.color}`} />
                      </div>
                      <h3 className="text-2xl font-bold text-white mb-1">{stat.value}</h3>
                      <p className="text-gray-400 text-sm">{stat.label}</p>
                    </div>
                  </motion.div>
                );
              })}
            </div>
          </div>
        </motion.div>

        {/* Main Footer Content */}
        <motion.div
          initial="hidden"
          whileInView="visible"
          viewport={{ once: true }}
          variants={containerVariants}
          className="py-16"
        >
          <div className="container mx-auto px-4">
            <div className="grid grid-cols-1 lg:grid-cols-5 gap-12">
              {/* Brand Section */}
              <motion.div variants={itemVariants} className="lg:col-span-2">
                <div className="mb-8">
                  <h3 className="text-3xl font-bold mb-4">
                    <span className="text-white">Pegasus</span>
                    <span className="text-purple-400"> Tool</span>
                  </h3>
                  <p className="text-gray-400 text-lg leading-relaxed mb-6">
                    The ultimate smartphone flashing and unlocking solution for repair professionals.
                    Trusted by technicians worldwide for reliable, fast, and secure device unlocking.
                  </p>

                  {/* Contact Info */}
                  <div className="space-y-3 mb-8">
                    <div className="flex items-center gap-3 text-gray-400">
                      <div className="w-8 h-8 bg-purple-500/20 rounded-full flex items-center justify-center">
                        <Mail className="w-4 h-4 text-purple-400" />
                      </div>
                      <span><EMAIL></span>
                    </div>
                    <div className="flex items-center gap-3 text-gray-400">
                      <div className="w-8 h-8 bg-blue-500/20 rounded-full flex items-center justify-center">
                        <Phone className="w-4 h-4 text-blue-400" />
                      </div>
                      <span>******-224-2157</span>
                    </div>
                    <div className="flex items-center gap-3 text-gray-400">
                      <div className="w-8 h-8 bg-green-500/20 rounded-full flex items-center justify-center">
                        <MapPin className="w-4 h-4 text-green-400" />
                      </div>
                      <span>Global Support Available</span>
                    </div>
                  </div>

                  {/* Social Links */}
                  <div>
                    <h4 className="text-white font-semibold mb-4">Follow Us</h4>
                    <div className="flex gap-3">
                      {socialLinks.map((social, index) => {
                        const Icon = social.icon;
                        return (
                          <motion.a
                            key={index}
                            href={social.href}
                            target="_blank"
                            rel="noopener noreferrer"
                            className={`w-10 h-10 bg-[#1a1a1a] border border-gray-700/50 rounded-full flex items-center justify-center text-gray-400 ${social.color} transition-all duration-300 hover:scale-110 hover:border-purple-400/50`}
                            whileHover={{ y: -2 }}
                            whileTap={{ scale: 0.95 }}
                          >
                            <Icon className="w-5 h-5" />
                            <span className="sr-only">{social.label}</span>
                          </motion.a>
                        );
                      })}
                    </div>
                  </div>
                </div>
              </motion.div>

              {/* Product Links */}
              <motion.div variants={itemVariants}>
                <h4 className="text-white font-semibold mb-6 flex items-center gap-2">
                  <Package className={`w-5 h-5 ${colors.text}`} />
                 Product
                </h4>
                <ul className="space-y-3">
                  {[
                    { to: "/", label: "Home" },
                    { to: "/software", label: "Software Mode" },
                    { to: "/hardware", label: "Hardware Mode" },
                    { to: "/whats-new", label: "What's New" },
                    { to: "/pricing", label: "Pricing" },
                  ].map((link, index) => (
                    <li key={index}>
                      <Link
                        to={link.to}
                        className={`text-gray-400 ${colors.hover.replace('bg-', 'text-').replace('/10', '')} transition-colors duration-300 flex items-center gap-2 group`}
                      >
                        <ChevronRight className="w-3 h-3 opacity-0 group-hover:opacity-100 transition-opacity" />
                        <span className="group-hover:translate-x-1 transition-transform duration-300">
                          {link.label}
                        </span>
                      </Link>
                    </li>
                  ))}
                </ul>
              </motion.div>

              {/* Sales & Business */}
              <motion.div variants={itemVariants}>
                <h4 className="text-white font-semibold mb-6 flex items-center gap-2">
                  <TrendingUp className={`w-5 h-5 ${colors.text}`} />
                  Sales & Business
                </h4>
                <ul className="space-y-3">
                  {[
                    { to: "/resellers", label: "Become a Reseller" },
                    { to: "/payment-methods", label: "Payment Methods" },
                    { to: "/contact", label: "Sales Inquiries" },
                    { to: "/help-center", label: "Business Support" },
                  ].map((link, index) => (
                    <li key={index}>
                      <Link
                        to={link.to}
                        className={`text-gray-400 ${colors.hover.replace('bg-', 'text-').replace('/10', '')} transition-colors duration-300 flex items-center gap-2 group`}
                      >
                        <ChevronRight className="w-3 h-3 opacity-0 group-hover:opacity-100 transition-opacity" />
                        <span className="group-hover:translate-x-1 transition-transform duration-300">
                          {link.label}
                        </span>
                      </Link>
                    </li>
                  ))}
                </ul>
              </motion.div>

              {/* Support & Resources */}
              <motion.div variants={itemVariants}>
                <h4 className="text-white font-semibold mb-6 flex items-center gap-2">
                  <Shield className={`w-5 h-5 ${colors.text}`} />
                  Support & Resources
                </h4>
                <ul className="space-y-3">
                  {[
                    { to: "/help-center", label: "Help Center" },
                    { to: "/knowledge-base", label: "Knowledge Base" },
                    { to: "/faq", label: "FAQ" },
                    { to: "/contact", label: "Contact Support" },
                    { to: "/terms-of-service", label: "Terms of Service" },
                    { to: "/privacy-policy", label: "Privacy Policy" },
                  ].map((link, index) => (
                    <li key={index}>
                      <Link
                        to={link.to}
                        className={`text-gray-400 ${colors.hover.replace('bg-', 'text-').replace('/10', '')} transition-colors duration-300 flex items-center gap-2 group`}
                      >
                        <ChevronRight className="w-3 h-3 opacity-0 group-hover:opacity-100 transition-opacity" />
                        <span className="group-hover:translate-x-1 transition-transform duration-300">
                          {link.label}
                        </span>
                      </Link>
                    </li>
                  ))}
                </ul>
              </motion.div>
            </div>
          </div>
        </motion.div>



        {/* Bottom Footer */}
        <motion.div
          initial="hidden"
          whileInView="visible"
          viewport={{ once: true }}
          variants={containerVariants}
          className="border-t border-gray-800/50 py-8"
        >
  <div className="container mx-auto px-4">
    <div className="flex justify-center items-center">
      <motion.div variants={itemVariants} className="flex items-center gap-2 text-gray-400">
        <span>© {new Date().getFullYear()} Pegasus Tools. All rights reserved.</span>
      </motion.div>
    </div>
  </div>
        </motion.div>
      </div>
    </footer>
  );
};

export default Footer;
