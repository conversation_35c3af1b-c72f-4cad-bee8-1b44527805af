import React, { useState } from 'react';
import { Card, CardContent } from '@/components/ui/card';
import { ChevronDown, ChevronUp, HelpCircle } from 'lucide-react';
import { motion, AnimatePresence } from 'framer-motion';
import { cn } from '@/lib/utils';

interface FAQItem {
  id: string;
  question: string;
  answer: string;
  category: 'general' | 'billing' | 'features' | 'support';
}

interface PricingFAQProps {
  theme?: 'software' | 'hardware';
}

const PricingFAQ: React.FC<PricingFAQProps> = ({ theme = 'software' }) => {
  const [openItems, setOpenItems] = useState<string[]>([]);

  const faqItems: FAQItem[] = [
    {
      id: '1',
      question: 'What happens when my plan expires?',
      answer: 'When your plan expires, you\'ll lose access to premium features but your account remains active. You can renew anytime to restore full access. No data is lost during the inactive period.',
      category: 'billing'
    },
    {
      id: '2',
      question: 'Can I upgrade or downgrade my plan anytime?',
      answer: 'Yes! You can upgrade your plan instantly. For downgrades, changes take effect at your next billing cycle. Any unused credits from your current plan will be preserved.',
      category: 'billing'
    },
    {
      id: '3',
      question: 'What payment methods do you accept?',
      answer: 'We accept all major credit cards, PayPal, and bank transfers. For enterprise plans, we also offer invoice billing with NET 30 terms.',
      category: 'billing'
    },
    {
      id: '4',
      question: 'Is there a free trial available?',
      answer: 'We offer a 7-day free trial for new users on any plan. No credit card required to start. You can explore all features before committing.',
      category: 'general'
    },
    {
      id: '5',
      question: 'What\'s included in AI Assistant access?',
      answer: 'AI Assistant helps with troubleshooting, repair guides, part identification, and technical support. Limited plans have message restrictions, while Unlimited plans have no limits.',
      category: 'features'
    },
    {
      id: '6',
      question: 'How do operation limits work?',
      answer: 'Each plan has monthly limits for different operations (unlock, read codes, etc.). Limits reset monthly. If you exceed limits, you can upgrade or purchase additional credits.',
      category: 'features'
    },
    {
      id: '7',
      question: 'Can I share my account with team members?',
      answer: 'Individual plans are for single users. For teams, we offer special multi-user discounts. Contact sales for custom team pricing and user management features.',
      category: 'general'
    },
    {
      id: '8',
      question: 'What support is included with each plan?',
      answer: 'Basic plans include email support. Pro and higher plans get priority support with faster response times. Max Pro Plus includes phone support and dedicated account management.',
      category: 'support'
    },
    {
      id: '9',
      question: 'Are there any setup fees or hidden costs?',
      answer: 'No setup fees or hidden costs. The price you see is what you pay. All features listed in your plan are included at no extra charge.',
      category: 'billing'
    },
    {
      id: '10',
      question: 'What happens to my data if I cancel?',
      answer: 'Your data is safely stored for 90 days after cancellation. You can reactivate anytime during this period. After 90 days, data is permanently deleted for security.',
      category: 'general'
    }
  ];

  const getThemeColors = () => {
    if (theme === 'hardware') {
      return {
        primary: 'text-pegasus-blue-600 dark:text-pegasus-blue-400',
        gradient: 'from-pegasus-blue-600 to-pegasus-blue-400',
        accent: 'pegasus-blue-500'
      };
    }
    return {
      primary: 'text-pegasus-orange',
      gradient: 'from-orange-600 to-orange-400',
      accent: 'orange-500'
    };
  };

  const themeColors = getThemeColors();

  const toggleItem = (id: string) => {
    setOpenItems(prev => 
      prev.includes(id) 
        ? prev.filter(item => item !== id)
        : [...prev, id]
    );
  };

  const getCategoryIcon = (category: string) => {
    switch (category) {
      case 'billing':
        return '💳';
      case 'features':
        return '⚡';
      case 'support':
        return '🎧';
      default:
        return '❓';
    }
  };

  const getCategoryColor = (category: string) => {
    switch (category) {
      case 'billing':
        return 'bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-400';
      case 'features':
        return 'bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-400';
      case 'support':
        return 'bg-purple-100 text-purple-800 dark:bg-purple-900/30 dark:text-purple-400';
      default:
        return 'bg-gray-100 text-gray-800 dark:bg-gray-900/30 dark:text-gray-400';
    }
  };

  return (
    <div className="py-16 bg-gray-50 dark:bg-gray-900/50">
      <div className="container mx-auto px-4">
        <div className="text-center mb-12">
          <div className="mb-4">
            <HelpCircle className={cn("h-12 w-12 mx-auto", themeColors.primary)} />
          </div>
          <h3 className="text-3xl font-bold text-gray-900 dark:text-white mb-4">
            Frequently Asked <span className={themeColors.primary}>Questions</span>
          </h3>
          <p className="text-gray-600 dark:text-gray-400 max-w-2xl mx-auto">
            Got questions about our pricing? We've got answers. Can't find what you're looking for? Contact our support team.
          </p>
        </div>

        <div className="max-w-4xl mx-auto">
          <div className="space-y-4">
            {faqItems.map((item, index) => (
              <motion.div
                key={item.id}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.3, delay: index * 0.05 }}
              >
                <Card className="overflow-hidden hover:shadow-md transition-shadow duration-200">
                  <CardContent className="p-0">
                    <button
                      onClick={() => toggleItem(item.id)}
                      className="w-full p-6 text-left hover:bg-gray-50 dark:hover:bg-gray-800/50 transition-colors duration-200"
                    >
                      <div className="flex items-center justify-between">
                        <div className="flex items-center gap-4 flex-1">
                          <div className={cn(
                            "px-2 py-1 rounded-full text-xs font-medium flex items-center gap-1",
                            getCategoryColor(item.category)
                          )}>
                            <span>{getCategoryIcon(item.category)}</span>
                            {item.category.charAt(0).toUpperCase() + item.category.slice(1)}
                          </div>
                          <h4 className="font-semibold text-gray-900 dark:text-white text-left">
                            {item.question}
                          </h4>
                        </div>
                        <div className="ml-4">
                          {openItems.includes(item.id) ? (
                            <ChevronUp className={cn("h-5 w-5", themeColors.primary)} />
                          ) : (
                            <ChevronDown className="h-5 w-5 text-gray-400" />
                          )}
                        </div>
                      </div>
                    </button>

                    <AnimatePresence>
                      {openItems.includes(item.id) && (
                        <motion.div
                          initial={{ height: 0, opacity: 0 }}
                          animate={{ height: 'auto', opacity: 1 }}
                          exit={{ height: 0, opacity: 0 }}
                          transition={{ duration: 0.3 }}
                          className="overflow-hidden"
                        >
                          <div className="px-6 pb-6 pt-0">
                            <div className="border-t border-gray-100 dark:border-gray-800 pt-4">
                              <p className="text-gray-700 dark:text-gray-300 leading-relaxed">
                                {item.answer}
                              </p>
                            </div>
                          </div>
                        </motion.div>
                      )}
                    </AnimatePresence>
                  </CardContent>
                </Card>
              </motion.div>
            ))}
          </div>
        </div>

        {/* Contact Support CTA */}
        <div className="mt-12 text-center">
          <div className="bg-white dark:bg-gray-800 rounded-2xl p-8 shadow-lg max-w-2xl mx-auto">
            <h4 className="text-xl font-bold text-gray-900 dark:text-white mb-4">
              Still have questions?
            </h4>
            <p className="text-gray-600 dark:text-gray-400 mb-6">
              Our support team is here to help you choose the right plan and answer any questions.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <button className={cn(
                "px-6 py-3 rounded-lg font-semibold text-white shadow-lg hover:shadow-xl transition-all duration-200 bg-gradient-to-r",
                themeColors.gradient
              )}>
                Contact Support
              </button>
              <button className="px-6 py-3 rounded-lg font-semibold text-gray-700 dark:text-gray-300 border border-gray-300 dark:border-gray-600 hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors duration-200">
                Schedule a Demo
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default PricingFAQ;
