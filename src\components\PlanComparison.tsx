import React, { useState } from 'react';
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Check, X, ChevronDown, ChevronUp } from 'lucide-react';
import { motion, AnimatePresence } from 'framer-motion';
import { cn } from '@/lib/utils';
import StatisticsStyleHeader from '@/components/layout/StatisticsStyleHeader';

interface PlanFeature {
  name: string;
  basic: boolean | string;
  plus: boolean | string;
  pro: boolean | string;
  max: boolean | string;
  maxPro: boolean | string;
  maxProPlus: boolean | string;
}

interface PlanComparisonProps {
  theme?: 'software' | 'hardware';
}

const PlanComparison: React.FC<PlanComparisonProps> = ({ theme = 'software' }) => {
  const [isExpanded, setIsExpanded] = useState(false);

  const features: PlanFeature[] = [
    {
      name: 'Unlock Operations',
      basic: '50',
      plus: '100',
      pro: '250',
      max: '500',
      maxPro: '1000',
      maxProPlus: '1500'
    },
    {
      name: 'Read Code Operations',
      basic: false,
      plus: false,
      pro: false,
      max: '100',
      maxPro: '200',
      maxProPlus: '300'
    },
    {
      name: 'Other Operations',
      basic: '250',
      plus: '500',
      pro: '1500',
      max: '2500',
      maxPro: 'Unlimited',
      maxProPlus: 'Unlimited'
    },
    {
      name: 'AI Assistant Access',
      basic: 'Limited',
      plus: 'Limited',
      pro: 'Limited',
      max: 'Limited',
      maxPro: 'Unlimited',
      maxProPlus: 'Unlimited'
    },
    {
      name: 'File Storage Backup',
      basic: '250 MB',
      plus: '500 MB',
      pro: '1 GB',
      max: '2 GB',
      maxPro: '4 GB',
      maxProPlus: '8 GB'
    },
    {
      name: 'Advanced Data Analysis',
      basic: false,
      plus: true,
      pro: true,
      max: true,
      maxPro: true,
      maxProPlus: true
    },
    {
      name: 'Priority Support',
      basic: false,
      plus: false,
      pro: true,
      max: true,
      maxPro: true,
      maxProPlus: true
    },
    {
      name: 'Sprint Unlock Access',
      basic: false,
      plus: false,
      pro: false,
      max: false,
      maxPro: false,
      maxProPlus: 'Limited'
    }
  ];

  const plans = [
    { key: 'basic', name: 'Basic', price: '$20', color: 'blue' },
    { key: 'plus', name: 'Plus', price: '$40', color: 'orange' },
    { key: 'pro', name: 'Pro', price: '$60', color: 'blue' },
    { key: 'max', name: 'Max', price: '$100', color: 'purple' },
    { key: 'maxPro', name: 'Max Pro', price: '$150', color: 'purple' },
    { key: 'maxProPlus', name: 'Max Pro Plus', price: '$250', color: 'purple' }
  ];

  const renderFeatureValue = (value: boolean | string, planColor: string) => {
    if (typeof value === 'boolean') {
      return value ? (
        <Check className={`h-5 w-5 text-green-500`} />
      ) : (
        <X className="h-5 w-5 text-gray-400" />
      );
    }
    return (
      <span className={cn(
        "font-semibold",
        planColor === 'orange' ? 'text-orange-600 dark:text-orange-400' :
        planColor === 'purple' ? 'text-purple-600 dark:text-purple-400' :
        'text-blue-600 dark:text-blue-400'
      )}>
        {value}
      </span>
    );
  };

  const getThemeColors = () => {
    if (theme === 'hardware') {
      return {
        primary: 'text-pegasus-blue-600 dark:text-pegasus-blue-400',
        gradient: 'from-pegasus-blue-600 to-pegasus-blue-400'
      };
    }
    return {
      primary: 'text-pegasus-orange',
      gradient: 'from-orange-600 to-orange-400'
    };
  };

  const themeColors = getThemeColors();

  const container = {
    hidden: { opacity: 0 },
    show: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1,
        delayChildren: 0.2
      }
    }
  };

  const item = {
    hidden: { opacity: 0, y: 20 },
    show: { opacity: 1, y: 0, transition: { duration: 0.5 } }
  };

  return (
    <div className="py-20 bg-[#111111] text-gray-300 relative overflow-hidden">
      <div className="absolute inset-0 z-1 pointer-events-none" style={{
        background: 'linear-gradient(to bottom, transparent 0%, #111111 90%), radial-gradient(ellipse at center, transparent 40%, #111111 95%)'
      }}></div>

      <div className="container mx-auto px-4 relative z-10">
        <StatisticsStyleHeader
          badge="Plan Comparison"
          title="Compare All Plans"
          highlightWord="Plans"
          subtitle="Choose the perfect plan that fits your needs. All plans include our core features with varying limits."
        />

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
          viewport={{ once: true }}
        >
          <Card className="overflow-hidden bg-[#1a1a1a] border border-gray-700/50 backdrop-blur-lg hover:border-purple-400/50 transition-all duration-300 hover:shadow-2xl hover:shadow-purple-500/20">
            <CardContent className="p-0">
              <div className="overflow-x-auto">
                <table className="w-full">
                  <thead>
                    <tr className="border-b border-gray-700">
                      <th className="text-left p-4 font-semibold text-white">
                        Features
                      </th>
                    {plans.map((plan) => (
                      <th key={plan.key} className="text-center p-4 min-w-[120px]">
                        <div className="font-bold text-white">
                          {plan.name}
                        </div>
                        <div className={cn(
                          "text-sm font-semibold mt-1",
                          plan.color === 'orange' ? 'text-orange-600 dark:text-orange-400' :
                          plan.color === 'purple' ? 'text-purple-600 dark:text-purple-400' :
                          'text-blue-600 dark:text-blue-400'
                        )}>
                          {plan.price}
                        </div>
                      </th>
                      ))}
                    </tr>
                  </thead>
                  <tbody>
                    <AnimatePresence>
                      {features.slice(0, isExpanded ? features.length : 4).map((feature, index) => (
                        <motion.tr
                          key={feature.name}
                          initial={{ opacity: 0, height: 0 }}
                          animate={{ opacity: 1, height: 'auto' }}
                          exit={{ opacity: 0, height: 0 }}
                          className="border-b border-gray-800 hover:bg-[#222222] transition-colors duration-200"
                        >
                        <td className="p-4 font-medium text-white">
                          {feature.name}
                        </td>
                        {plans.map((plan) => (
                          <td key={plan.key} className="p-4 text-center">
                            {renderFeatureValue(feature[plan.key as keyof PlanFeature] as boolean | string, plan.color)}
                          </td>
                        ))}
                      </motion.tr>
                    ))}
                    </AnimatePresence>
                  </tbody>
                </table>
              </div>
            </CardContent>
          </Card>
        </motion.div>

        <motion.div
          className="text-center mt-8"
          initial={{ opacity: 0 }}
          whileInView={{ opacity: 1 }}
          transition={{ duration: 0.6, delay: 0.2 }}
          viewport={{ once: true }}
        >
          <Button
            onClick={() => setIsExpanded(!isExpanded)}
            variant="outline"
            className="gap-2 bg-[#1a1a1a] border-gray-700/50 text-white hover:bg-[#222222] hover:border-purple-400/50 transition-all duration-300"
          >
            {isExpanded ? (
              <>
                Show Less <ChevronUp className="h-4 w-4" />
              </>
            ) : (
              <>
                Show All Features <ChevronDown className="h-4 w-4" />
              </>
            )}
          </Button>
        </motion.div>
      </div>
    </div>
  );
};

export default PlanComparison;
