import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { X, Clock, Gift, Zap, Tag } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { cn } from '@/lib/utils';

interface AdvancedOffer {
  id: string;
  title: string;
  description: string;
  discount_type: 'percentage' | 'fixed' | 'bogo' | 'tiered';
  discount_value: number;
  applicable_plans: string[];
  end_date: string;
  urgency_level: 'low' | 'medium' | 'high';
  badge_text: string;
  promo_code?: string;
}

interface AdvancedOfferBannerProps {
  offer: AdvancedOffer;
  onClose?: () => void;
  onApplyCode?: (code: string) => void;
  theme?: 'software' | 'hardware';
}

const AdvancedOfferBanner: React.FC<AdvancedOfferBannerProps> = ({
  offer,
  onClose,
  onApplyCode,
  theme = 'software'
}) => {
  const [timeLeft, setTimeLeft] = useState<{
    days: number;
    hours: number;
    minutes: number;
    seconds: number;
  }>({ days: 0, hours: 0, minutes: 0, seconds: 0 });
  
  const [promoCode, setPromoCode] = useState('');
  const [showPromoInput, setShowPromoInput] = useState(false);

  useEffect(() => {
    const calculateTimeLeft = () => {
      const endTime = new Date(offer.end_date).getTime();
      const now = new Date().getTime();
      const difference = endTime - now;

      if (difference > 0) {
        const days = Math.floor(difference / (1000 * 60 * 60 * 24));
        const hours = Math.floor((difference % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
        const minutes = Math.floor((difference % (1000 * 60 * 60)) / (1000 * 60));
        const seconds = Math.floor((difference % (1000 * 60)) / 1000);

        setTimeLeft({ days, hours, minutes, seconds });
      } else {
        setTimeLeft({ days: 0, hours: 0, minutes: 0, seconds: 0 });
      }
    };

    calculateTimeLeft();
    const timer = setInterval(calculateTimeLeft, 1000);

    return () => clearInterval(timer);
  }, [offer.end_date]);

  const getUrgencyColors = () => {
    switch (offer.urgency_level) {
      case 'high':
        return {
          gradient: 'from-red-600 via-orange-600 to-yellow-500',
          border: 'border-red-500/30',
          text: 'text-white',
          badge: 'bg-red-500/20 text-red-100'
        };
      case 'medium':
        return {
          gradient: 'from-orange-600 via-amber-600 to-yellow-500',
          border: 'border-orange-500/30',
          text: 'text-white',
          badge: 'bg-orange-500/20 text-orange-100'
        };
      default:
        return {
          gradient: theme === 'hardware' ? 'from-pegasus-blue-600 to-pegasus-blue-400' : 'from-blue-600 to-cyan-500',
          border: theme === 'hardware' ? 'border-pegasus-blue-500/30' : 'border-blue-500/30',
          text: 'text-white',
          badge: theme === 'hardware' ? 'bg-pegasus-blue-500/20 text-pegasus-blue-100' : 'bg-blue-500/20 text-blue-100'
        };
    }
  };

  const getDiscountText = () => {
    switch (offer.discount_type) {
      case 'percentage':
        return `${offer.discount_value}% OFF`;
      case 'fixed':
        return `$${offer.discount_value} OFF`;
      case 'bogo':
        return 'BUY 1 GET 1';
      case 'tiered':
        return `UP TO ${offer.discount_value}% OFF`;
      default:
        return 'SPECIAL OFFER';
    }
  };

  const getOfferIcon = () => {
    switch (offer.discount_type) {
      case 'percentage':
        return <Tag className="h-6 w-6" />;
      case 'fixed':
        return <Gift className="h-6 w-6" />;
      case 'bogo':
        return <Zap className="h-6 w-6" />;
      default:
        return <Gift className="h-6 w-6" />;
    }
  };

  const colors = getUrgencyColors();

  const handleApplyCode = () => {
    if (promoCode.trim() && onApplyCode) {
      onApplyCode(promoCode.trim());
      setShowPromoInput(false);
      setPromoCode('');
    }
  };

  return (
    <motion.div
      initial={{ opacity: 0, y: -100, scale: 0.95 }}
      animate={{ opacity: 1, y: 0, scale: 1 }}
      exit={{ opacity: 0, y: -100, scale: 0.95 }}
      transition={{ duration: 0.5, type: "spring", stiffness: 100 }}
      className={cn(
        "relative overflow-hidden rounded-2xl shadow-2xl border-2 mb-8",
        colors.border
      )}
    >
      {/* Animated Background */}
      <div className={cn(
        "absolute inset-0 bg-gradient-to-r opacity-90",
        colors.gradient
      )}>
        <div className="absolute inset-0 bg-black/10"></div>
        {/* Animated particles */}
        <div className="absolute inset-0 overflow-hidden">
          {[...Array(20)].map((_, i) => (
            <motion.div
              key={i}
              className="absolute w-2 h-2 bg-white/20 rounded-full"
              animate={{
                x: [0, 100, 0],
                y: [0, -50, 0],
                opacity: [0, 1, 0],
              }}
              transition={{
                duration: 3 + Math.random() * 2,
                repeat: Infinity,
                delay: Math.random() * 2,
              }}
              style={{
                left: `${Math.random() * 100}%`,
                top: `${Math.random() * 100}%`,
              }}
            />
          ))}
        </div>
      </div>

      {/* Close Button */}
      {onClose && (
        <button
          onClick={onClose}
          className="absolute top-4 right-4 z-20 p-2 rounded-full bg-black/20 hover:bg-black/40 transition-colors duration-200"
        >
          <X className="h-4 w-4 text-white" />
        </button>
      )}

      {/* Content */}
      <div className="relative z-10 p-6 md:p-8">
        <div className="flex flex-col md:flex-row items-center justify-between gap-6">
          {/* Left Side - Offer Info */}
          <div className="flex items-center gap-4 text-center md:text-left">
            <div className="flex-shrink-0">
              <div className={cn(
                "p-3 rounded-full",
                colors.badge
              )}>
                {getOfferIcon()}
              </div>
            </div>
            
            <div>
              <div className="flex items-center gap-2 mb-2">
                <span className={cn(
                  "px-3 py-1 rounded-full text-xs font-bold",
                  colors.badge
                )}>
                  {offer.badge_text}
                </span>
                <span className="text-2xl font-bold text-white">
                  {getDiscountText()}
                </span>
              </div>
              
              <h3 className="text-xl md:text-2xl font-bold text-white mb-1">
                {offer.title}
              </h3>
              
              <p className="text-white/90 text-sm md:text-base">
                {offer.description}
              </p>
            </div>
          </div>

          {/* Right Side - Timer & Actions */}
          <div className="flex flex-col items-center gap-4">
            {/* Countdown Timer */}
            <div className="flex items-center gap-2 text-white">
              <Clock className="h-5 w-5" />
              <div className="flex gap-2 font-mono font-bold">
                <div className="text-center">
                  <div className="text-lg">{timeLeft.days.toString().padStart(2, '0')}</div>
                  <div className="text-xs opacity-75">DAYS</div>
                </div>
                <div className="text-lg">:</div>
                <div className="text-center">
                  <div className="text-lg">{timeLeft.hours.toString().padStart(2, '0')}</div>
                  <div className="text-xs opacity-75">HRS</div>
                </div>
                <div className="text-lg">:</div>
                <div className="text-center">
                  <div className="text-lg">{timeLeft.minutes.toString().padStart(2, '0')}</div>
                  <div className="text-xs opacity-75">MIN</div>
                </div>
                <div className="text-lg">:</div>
                <div className="text-center">
                  <div className="text-lg">{timeLeft.seconds.toString().padStart(2, '0')}</div>
                  <div className="text-xs opacity-75">SEC</div>
                </div>
              </div>
            </div>

            {/* Promo Code Section */}
            {offer.promo_code && (
              <div className="flex flex-col items-center gap-2">
                <AnimatePresence>
                  {showPromoInput ? (
                    <motion.div
                      initial={{ opacity: 0, scale: 0.8 }}
                      animate={{ opacity: 1, scale: 1 }}
                      exit={{ opacity: 0, scale: 0.8 }}
                      className="flex gap-2"
                    >
                      <Input
                        value={promoCode}
                        onChange={(e) => setPromoCode(e.target.value)}
                        placeholder="Enter promo code"
                        className="bg-white/20 border-white/30 text-white placeholder:text-white/70"
                        onKeyPress={(e) => e.key === 'Enter' && handleApplyCode()}
                      />
                      <Button
                        onClick={handleApplyCode}
                        variant="secondary"
                        size="sm"
                      >
                        Apply
                      </Button>
                    </motion.div>
                  ) : (
                    <Button
                      onClick={() => setShowPromoInput(true)}
                      variant="secondary"
                      size="sm"
                      className="bg-white/20 hover:bg-white/30 text-white border-white/30"
                    >
                      Use Code: {offer.promo_code}
                    </Button>
                  )}
                </AnimatePresence>
              </div>
            )}
          </div>
        </div>
      </div>
    </motion.div>
  );
};

export default AdvancedOfferBanner;
