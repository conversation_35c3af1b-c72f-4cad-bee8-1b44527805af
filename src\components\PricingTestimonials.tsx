import React from 'react';
import { Card, CardContent } from '@/components/ui/card';
import { Star, Quote } from 'lucide-react';
import { motion } from 'framer-motion';
import { cn } from '@/lib/utils';

interface Testimonial {
  id: string;
  name: string;
  role: string;
  company: string;
  content: string;
  rating: number;
  plan: string;
  avatar: string;
}

interface PricingTestimonialsProps {
  theme?: 'software' | 'hardware';
}

const PricingTestimonials: React.FC<PricingTestimonialsProps> = ({ theme = 'software' }) => {
  const testimonials: Testimonial[] = [
    {
      id: '1',
      name: '<PERSON>',
      role: 'Mobile Repair Technician',
      company: 'TechFix Pro',
      content: 'The Max Pro plan has transformed my repair business. The unlimited AI access helps me solve complex issues faster than ever.',
      rating: 5,
      plan: 'Max Pro',
      avatar: '👨‍🔧'
    },
    {
      id: '2',
      name: '<PERSON>',
      role: 'Shop Owner',
      company: 'Mobile Solutions',
      content: 'Started with Basic, upgraded to Plus. The value is incredible - saved me hours of troubleshooting every week.',
      rating: 5,
      plan: 'Plus',
      avatar: '👩‍💼'
    },
    {
      id: '3',
      name: '<PERSON>',
      role: 'Senior Technician',
      company: 'Phone Clinic',
      content: 'The Pro plan gives me everything I need for my repair shop. The 3-month validity is perfect for my workflow.',
      rating: 5,
      plan: 'Pro',
      avatar: '👨‍💻'
    }
  ];

  const getThemeColors = () => {
    if (theme === 'hardware') {
      return {
        primary: 'text-pegasus-blue-600 dark:text-pegasus-blue-400',
        gradient: 'from-pegasus-blue-600 to-pegasus-blue-400',
        accent: 'pegasus-blue'
      };
    }
    return {
      primary: 'text-pegasus-orange',
      gradient: 'from-orange-600 to-orange-400',
      accent: 'orange'
    };
  };

  const themeColors = getThemeColors();

  const renderStars = (rating: number) => {
    return Array.from({ length: 5 }, (_, i) => (
      <Star
        key={i}
        className={cn(
          "h-4 w-4",
          i < rating ? "text-yellow-400 fill-current" : "text-gray-300"
        )}
      />
    ));
  };

  const getPlanColor = (plan: string) => {
    switch (plan.toLowerCase()) {
      case 'basic':
        return 'bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-400';
      case 'plus':
        return 'bg-orange-100 text-orange-800 dark:bg-orange-900/30 dark:text-orange-400';
      case 'pro':
        return 'bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-400';
      case 'max':
      case 'max pro':
      case 'max pro plus':
        return 'bg-purple-100 text-purple-800 dark:bg-purple-900/30 dark:text-purple-400';
      default:
        return 'bg-gray-100 text-gray-800 dark:bg-gray-900/30 dark:text-gray-400';
    }
  };

  return (
    <div className="py-16 bg-white dark:bg-gray-900">
      <div className="container mx-auto px-4">
        <div className="text-center mb-12">
          <h3 className="text-3xl font-bold text-gray-900 dark:text-white mb-4">
            What Our <span className={themeColors.primary}>Customers Say</span>
          </h3>
          <p className="text-gray-600 dark:text-gray-400 max-w-2xl mx-auto">
            Join thousands of satisfied technicians who trust our tools for their daily repairs
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 max-w-6xl mx-auto">
          {testimonials.map((testimonial, index) => (
            <motion.div
              key={testimonial.id}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: index * 0.1 }}
            >
              <Card className="h-full hover:shadow-lg transition-shadow duration-300">
                <CardContent className="p-6">
                  {/* Quote Icon */}
                  <div className="mb-4">
                    <Quote className={cn("h-8 w-8", themeColors.primary)} />
                  </div>

                  {/* Rating */}
                  <div className="flex items-center gap-1 mb-4">
                    {renderStars(testimonial.rating)}
                  </div>

                  {/* Content */}
                  <p className="text-gray-700 dark:text-gray-300 mb-6 leading-relaxed">
                    "{testimonial.content}"
                  </p>

                  {/* Author Info */}
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-3">
                      <div className="text-2xl">{testimonial.avatar}</div>
                      <div>
                        <h4 className="font-semibold text-gray-900 dark:text-white">
                          {testimonial.name}
                        </h4>
                        <p className="text-sm text-gray-600 dark:text-gray-400">
                          {testimonial.role}
                        </p>
                        <p className="text-xs text-gray-500 dark:text-gray-500">
                          {testimonial.company}
                        </p>
                      </div>
                    </div>

                    {/* Plan Badge */}
                    <div className={cn(
                      "px-3 py-1 rounded-full text-xs font-semibold",
                      getPlanColor(testimonial.plan)
                    )}>
                      {testimonial.plan}
                    </div>
                  </div>
                </CardContent>
              </Card>
            </motion.div>
          ))}
        </div>

        {/* Trust Indicators */}
        <div className="mt-16 text-center">
          <div className="grid grid-cols-2 md:grid-cols-4 gap-8 max-w-4xl mx-auto">
            <div className="text-center">
              <div className="text-3xl font-bold text-gray-900 dark:text-white mb-2">
                10,000+
              </div>
              <div className="text-sm text-gray-600 dark:text-gray-400">
                Happy Customers
              </div>
            </div>
            <div className="text-center">
              <div className="text-3xl font-bold text-gray-900 dark:text-white mb-2">
                500,000+
              </div>
              <div className="text-sm text-gray-600 dark:text-gray-400">
                Successful Repairs
              </div>
            </div>
            <div className="text-center">
              <div className="text-3xl font-bold text-gray-900 dark:text-white mb-2">
                99.9%
              </div>
              <div className="text-sm text-gray-600 dark:text-gray-400">
                Uptime
              </div>
            </div>
            <div className="text-center">
              <div className="text-3xl font-bold text-gray-900 dark:text-white mb-2">
                24/7
              </div>
              <div className="text-sm text-gray-600 dark:text-gray-400">
                Support
              </div>
            </div>
          </div>
        </div>

        {/* Money Back Guarantee */}
        <div className="mt-12 text-center">
          <div className={cn(
            "inline-flex items-center gap-2 bg-gradient-to-r px-6 py-3 rounded-full text-white font-semibold shadow-lg",
            `${themeColors.gradient}`
          )}>
            <span className="text-xl">🛡️</span>
            30-Day Money Back Guarantee
          </div>
        </div>
      </div>
    </div>
  );
};

export default PricingTestimonials;
