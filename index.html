<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />

    <!-- ✅ عنوان احترافي -->
    <title>Pegasus Tools – Complete Software & Hardware Solution</title>

    <!-- ✅ وصف دقيق وموثوق -->
    <meta name="description" content="Pegasus Tools is a professional smartphone repair and unlocking system with integrated software tools and detailed hardware documentation for technicians worldwide." />
    <meta name="author" content="Pegasus Team" />

    <!-- ✅ OG Meta Tags (Facebook) -->
    <meta property="og:title" content="Pegasus Tools – Complete Software & Hardware Solution" />
    <meta property="og:description" content="Smartphone unlocking system with software and hardware support – built for repair professionals." />
    <meta property="og:type" content="website" />
    <meta property="og:image" content="https://www.pegasus-tools.com/favicon.ico" />
    <meta property="og:url" content="https://www.pegasus-tools.com" />

    <!-- ✅ Twitter Meta Tags -->
    <meta name="twitter:card" content="summary_large_image" />
    <meta name="twitter:site" content="@pegasus_tools" />
    <meta name="twitter:image" content="https://www.pegasus-tools.com/favicon.ico" />

    <!-- ✅ Structured Data: Organization + Website -->
    <script type="application/ld+json">
    {
      "@context": "https://schema.org",
      "@graph": [
        {
          "@type": "Organization",
          "name": "Pegasus Tools",
          "url": "https://www.pegasus-tools.com",
          "logo": {
            "@type": "ImageObject",
            "url": "https://www.pegasus-tools.com/favicon.ico"
          },
          "sameAs": [
            "https://www.pegasus-tools.com",
            "https://twitter.com/pegasus_tools"
          ]
        },
        {
          "@type": "WebSite",
          "name": "Pegasus Tools",
          "url": "https://www.pegasus-tools.com",
          "publisher": {
            "@id": "https://www.pegasus-tools.com"
          }
        }
      ]
    }
    </script>
  </head>

  <body>
    <div id="root"></div>

    <!-- Tawk.to Live Chat -->
    <script type="text/javascript">
      var Tawk_API=Tawk_API||{}, Tawk_LoadStart=new Date();
      (function(){
        var s1=document.createElement("script"),s0=document.getElementsByTagName("script")[0];
        s1.async=true;
        s1.src='https://embed.tawk.to/68254dbfe6bf69190cdb34c4/1ir8rfd4h';
        s1.charset='UTF-8';
        s1.setAttribute('crossorigin','*');
        s0.parentNode.insertBefore(s1,s0);
      })();
    </script>

    <!-- Scripts -->
    <script src="https://cdn.gpteng.co/gptengineer.js" type="module"></script>
    <script type="module" src="/src/main.tsx"></script>
  </body>
</html>
