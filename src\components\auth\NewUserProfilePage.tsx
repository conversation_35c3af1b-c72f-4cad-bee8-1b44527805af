import { useState, useEffect } from "react";
import { useAuth } from "@/contexts/AuthContext";
import ProtectedRoute from "./ProtectedRoute";
import { motion, AnimatePresence } from "framer-motion";
import {
  Settings, Shield, User, Bell, Key, Palette, ArrowLeft, Save, Edit3,
  Mail, Calendar, MapPin, Phone, Globe, Award, Activity,
  Download, Star, Clock, CheckCircle, AlertCircle, Loader2, Eye,
  RefreshCw, Smartphone, Monitor, Trash2, ChevronLeft, ChevronRight
} from "lucide-react";
import { Button } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Link } from "react-router-dom";
import { getUserDisplayName, getUserAvatar } from "@/lib/auth";
import { toast } from "sonner";
import { useUserProfile } from "@/hooks/useUserProfile";
import { supabase } from "@/integrations/supabase/client";
import QRCode from "qrcode";

// Interfaces for new functionality
interface UserSession {
  id: string;
  device: string;
  browser: string;
  location: string;
  lastActive: string;
  isCurrent: boolean;
}

interface OperationDetail {
  operation_id: string;
  operation_type: string;
  time: string;
  status: string;
  model?: string;
  brand?: string;
  android?: string;
  baseband?: string;
  carrier?: string;
  credit?: string;
  hwid?: string;
  imei?: string;
  phone_sn?: string;
  security_patch?: string;
  username?: string;
}

export const NewUserProfilePage = () => {
  const { user, updateProfile } = useAuth();
  const { profileData, userStats, loading: profileLoading, error, updateUserProfile } = useUserProfile(user);
  const [isEditing, setIsEditing] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [activeTab, setActiveTab] = useState('profile');

  // 2FA State
  const [twoFactorEnabled, setTwoFactorEnabled] = useState(false);
  const [qrCodeUrl, setQrCodeUrl] = useState<string>("");
  const [twoFactorSecret, setTwoFactorSecret] = useState<string>("");
  const [verificationCode, setVerificationCode] = useState<string>("");
  const [showQrCode, setShowQrCode] = useState(false);

  // Activity pagination
  const [currentPage, setCurrentPage] = useState(1);
  const [allOperations, setAllOperations] = useState<OperationDetail[]>([]);
  const [totalOperations, setTotalOperations] = useState(0);
  const [selectedOperation, setSelectedOperation] = useState<OperationDetail | null>(null);
  const [showOperationDetails, setShowOperationDetails] = useState(false);

  // Sessions state
  const [userSessions, setUserSessions] = useState<UserSession[]>([]);
  const [sessionsLoading, setSessionsLoading] = useState(false);

  // HWID Reset state
  const [showHwidResetDialog, setShowHwidResetDialog] = useState(false);
  const [hwidResetLoading, setHwidResetLoading] = useState(false);

  const [formData, setFormData] = useState({
    full_name: "",
    email: "",
    phone: "",
    location: "",
    bio: "",
  });

  // Initialize form data when user and profile data are loaded
  useEffect(() => {
    if (user && profileData) {
      setFormData({
        full_name: profileData.name || getUserDisplayName(user),
        email: user.email || "",
        phone: profileData.phone || user.user_metadata?.phone || "",
        location: profileData.country || user.user_metadata?.location || "",
        bio: user.user_metadata?.bio || "",
      });
      setTwoFactorEnabled(profileData.two_factor_enabled || false);
    }
  }, [user, profileData]);

  // Generate 2FA secret and QR code
  const generateTwoFactorSecret = async () => {
    try {
      const secret = Array.from(crypto.getRandomValues(new Uint8Array(20)))
        .map(b => b.toString(36))
        .join('')
        .substring(0, 32);

      const serviceName = "Pegasus Tools";
      const accountName = user?.email || "user";
      const otpAuthUrl = `otpauth://totp/${encodeURIComponent(serviceName)}:${encodeURIComponent(accountName)}?secret=${secret}&issuer=${encodeURIComponent(serviceName)}`;

      const qrCodeDataUrl = await QRCode.toDataURL(otpAuthUrl);

      setTwoFactorSecret(secret);
      setQrCodeUrl(qrCodeDataUrl);
      setShowQrCode(true);
    } catch (error) {
      console.error('Error generating 2FA secret:', error);
      toast.error("Failed to generate 2FA secret");
    }
  };

  // Verify and enable 2FA
  const verifyAndEnable2FA = async () => {
    if (!verificationCode || !twoFactorSecret) {
      toast.error("Please enter the verification code");
      return;
    }

    try {
      setIsLoading(true);

      // Store the secret in the database
      const { error } = await updateUserProfile({
        otp_secret: twoFactorSecret,
        two_factor_enabled: true,
      });

      if (error) {
        toast.error("Failed to enable 2FA");
        return;
      }

      setTwoFactorEnabled(true);
      setShowQrCode(false);
      setVerificationCode("");
      toast.success("Two-factor authentication enabled successfully!");
    } catch (error) {
      console.error('Error enabling 2FA:', error);
      toast.error("Failed to enable 2FA");
    } finally {
      setIsLoading(false);
    }
  };

  // Disable 2FA
  const disable2FA = async () => {
    try {
      setIsLoading(true);

      const { error } = await updateUserProfile({
        otp_secret: null,
        two_factor_enabled: false,
      });

      if (error) {
        toast.error("Failed to disable 2FA");
        return;
      }

      setTwoFactorEnabled(false);
      setTwoFactorSecret("");
      setQrCodeUrl("");
      toast.success("Two-factor authentication disabled");
    } catch (error) {
      console.error('Error disabling 2FA:', error);
      toast.error("Failed to disable 2FA");
    } finally {
      setIsLoading(false);
    }
  };

  // Reset HWID with 15-day deduction
  const resetHwid = async () => {
    if (!profileData) return;
  
    try {
      setHwidResetLoading(true);
  
      // Calculate new expiry date (subtract 15 days)
      let newExpiryTime = null;
      if (profileData.expiry_time) {
        const currentExpiry = new Date(profileData.expiry_time);
        currentExpiry.setDate(currentExpiry.getDate() - 15);
  
        // ✅ فقط التاريخ بدون الوقت
        newExpiryTime = currentExpiry.toISOString().split("T")[0];
      }
  
      const { error } = await updateUserProfile({
        hwid: 'Null',
        expiry_time: newExpiryTime,  // ← الآن يتم حفظه كـ "2025-07-22"
      });
  
      if (error) {
        toast.error("Failed to reset HWID");
        return;
      }
  
      setShowHwidResetDialog(false);
      toast.success("HWID reset successfully. 15 days deducted from account expiration.");
    } catch (error) {
      console.error('Error resetting HWID:', error);
      toast.error("Failed to reset HWID");
    } finally {
      setHwidResetLoading(false);
    }
  };
  

  const handleSave = async () => {
    setIsLoading(true);
    try {
      // Update the users table
      const { error: profileError } = await updateUserProfile({
        name: formData.full_name,
        phone: formData.phone,
        country: formData.location,
      });

      if (profileError) {
        toast.error(profileError);
        return;
      }

      // Update auth user metadata
      const { error: authError } = await updateProfile({
        data: {
          full_name: formData.full_name,
          phone: formData.phone,
          location: formData.location,
          bio: formData.bio,
        }
      });

      if (!authError) {
        setIsEditing(false);
        toast.success("Profile updated successfully!");
      } else {
        toast.error("Failed to update auth profile");
      }
    } catch (err) {
      console.error('Error updating profile:', err);
      toast.error("Failed to update profile");
    } finally {
      setIsLoading(false);
    }
  };

  // Fetch all operations with pagination
  const fetchAllOperations = async () => {
    if (!user) return;

    try {
      const { data: operations, error } = await supabase
        .from('operations')
        .select('*')
        .eq('uid', user.id)
        .order('time', { ascending: false });

      if (error) throw error;

      setAllOperations(operations || []);
      setTotalOperations(operations?.length || 0);
    } catch (error) {
      console.error('Error fetching operations:', error);
      toast.error("Failed to fetch operations");
    }
  };

  // Get paginated operations
  const getPaginatedOperations = () => {
    const startIndex = (currentPage - 1) * 5;
    const endIndex = startIndex + 5;
    return allOperations.slice(startIndex, endIndex);
  };

  // Get total pages
  const getTotalPages = () => {
    return Math.ceil(totalOperations / 5);
  };

  // Fetch user sessions (mock implementation - replace with real session tracking)
  const fetchUserSessions = async () => {
    setSessionsLoading(true);
    try {
      // This is a mock implementation. In a real app, you'd fetch from a sessions table
      // For now, we'll create some realistic mock data based on the current user
      const mockSessions: UserSession[] = [
        {
          id: "current",
          device: "Windows PC",
          browser: "Chrome",
          location: "Current Location",
          lastActive: "Now",
          isCurrent: true,
        },
        // Add more sessions based on actual login history if available
      ];

      setUserSessions(mockSessions);
    } catch (error) {
      console.error('Error fetching sessions:', error);
      toast.error("Failed to fetch sessions");
    } finally {
      setSessionsLoading(false);
    }
  };

  // Revoke session
  const revokeSession = async (sessionId: string) => {
    try {
      // In a real implementation, you'd revoke the session from the database
      setUserSessions(prev => prev.filter(session => session.id !== sessionId));
      toast.success("Session revoked successfully");
    } catch (error) {
      console.error('Error revoking session:', error);
      toast.error("Failed to revoke session");
    }
  };

  // Load data on component mount
  useEffect(() => {
    if (user) {
      fetchAllOperations();
      fetchUserSessions();
    }
  }, [user]);

  // Animation variants
  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        duration: 0.6,
        staggerChildren: 0.1
      }
    }
  };

  const itemVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: {
      opacity: 1,
      y: 0,
      transition: { duration: 0.5 }
    }
  };

  const cardVariants = {
    hidden: { opacity: 0, scale: 0.95 },
    visible: {
      opacity: 1,
      scale: 1,
      transition: { duration: 0.4 }
    }
  };

  const tabs = [
    { id: 'profile', label: 'Profile', icon: User },
    { id: 'settings', label: 'Settings', icon: Settings },
    { id: 'security', label: 'Security', icon: Shield },
    { id: 'activity', label: 'Activity', icon: Activity },
  ];

  // Show loading state
  if (!user || profileLoading) {
    return (
      <ProtectedRoute>
        <div className="min-h-screen bg-[#111111] text-gray-300 flex items-center justify-center">
          <div className="flex items-center gap-3">
            <Loader2 className="w-6 h-6 animate-spin text-purple-400" />
            <span className="text-lg">Loading profile...</span>
          </div>
        </div>
      </ProtectedRoute>
    );
  }

  // Show error state
  if (error) {
    return (
      <ProtectedRoute>
        <div className="min-h-screen bg-[#111111] text-gray-300 flex items-center justify-center">
          <div className="text-center">
            <AlertCircle className="w-12 h-12 text-red-400 mx-auto mb-4" />
            <h2 className="text-xl font-semibold mb-2">Error Loading Profile</h2>
            <p className="text-gray-400 mb-4">{error}</p>
            <Button onClick={() => window.location.reload()} className="bg-purple-600 hover:bg-purple-700">
              Retry
            </Button>
          </div>
        </div>
      </ProtectedRoute>
    );
  }

  const userName = getUserDisplayName(user);
  const userAvatar = getUserAvatar(user);

  return (
    <ProtectedRoute>
      <div className="min-h-screen bg-[#111111] text-gray-300 relative overflow-hidden">
        {/* Background Effects */}
        <div className="absolute inset-0 z-0 pointer-events-none" style={{
          background: 'linear-gradient(to bottom, transparent 0%, #111111 90%), radial-gradient(ellipse at center, transparent 40%, #111111 95%)'
        }}></div>

        {/* Animated particles */}
        {[...Array(8)].map((_, i) => (
          <motion.div
            key={i}
            className="absolute rounded-full bg-purple-500/5"
            style={{
              width: Math.random() * 100 + 50,
              height: Math.random() * 100 + 50,
              top: `${Math.random() * 100}%`,
              left: `${Math.random() * 100}%`,
            }}
            animate={{
              y: [0, -200, 0],
              x: [0, Math.random() * 100 - 50, 0],
              opacity: [0, 0.3, 0],
            }}
            transition={{
              duration: Math.random() * 20 + 15,
              repeat: Infinity,
              delay: Math.random() * 10,
            }}
          />
        ))}

        <div className="relative z-10 py-8 px-4">
          <motion.div
            variants={containerVariants}
            initial="hidden"
            animate="visible"
            className="max-w-7xl mx-auto space-y-8"
          >
            {/* Header */}
            <motion.div variants={itemVariants} className="mb-12">
              <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-6">
                <div className="flex flex-col gap-4">
                  <Link to="/" className="self-start">
                    <Button
                      variant="ghost"
                      size="sm"
                      className="text-gray-400 hover:text-white hover:bg-gray-800/50 transition-all duration-300 border border-gray-700/50 hover:border-purple-400/50 px-4 py-2"
                    >
                      <ArrowLeft className="w-4 h-4 mr-2" />
                      Back to Home
                    </Button>
                  </Link>
                  <div>
                    <h1 className="text-4xl font-bold text-white mb-2">Profile Dashboard</h1>
                    <p className="text-gray-400">Manage your account settings and preferences</p>
                  </div>
                </div>

                {/* Credits and Subscription Display */}
                {profileData && (
                  <div className="flex flex-col sm:flex-row gap-4 lg:gap-6">
                    <div className="bg-gradient-to-r from-purple-600/20 to-purple-500/10 border border-purple-500/30 rounded-xl p-4 backdrop-blur-lg">
                      <div className="flex items-center gap-3">
                        <div className="w-10 h-10 bg-purple-500/20 rounded-full flex items-center justify-center">
                          <Award className="w-5 h-5 text-purple-400" />
                        </div>
                        <div>
                          <p className="text-gray-400 text-sm">Credits Balance</p>
                          <p className="text-white font-bold text-lg">{profileData.credits || '0'}</p>
                        </div>
                      </div>
                    </div>

                    <div className="bg-gradient-to-r from-blue-600/20 to-blue-500/10 border border-blue-500/30 rounded-xl p-4 backdrop-blur-lg">
                      <div className="flex items-center gap-3">
                        <div className="w-10 h-10 bg-blue-500/20 rounded-full flex items-center justify-center">
                          <Star className="w-5 h-5 text-blue-400" />
                        </div>
                        <div>
                          <p className="text-gray-400 text-sm">Subscription Plan</p>
                          <p className="text-white font-bold text-lg capitalize">{profileData.my_plans || 'Basic'}</p>
                        </div>
                      </div>
                    </div>
                  </div>
                )}
              </div>
            </motion.div>

            {/* Hero Profile Section */}
            <motion.div variants={itemVariants} className="mb-12">
              <div className="bg-gradient-to-r from-purple-600/20 via-purple-500/10 to-purple-700/20 rounded-2xl p-8 border border-gray-700/50 backdrop-blur-lg relative overflow-hidden">
                <div className="absolute inset-0 bg-[url('/patterns/grid.svg')] opacity-5"></div>

                <div className="relative z-10 flex flex-col lg:flex-row items-center gap-8">
                  <div className="relative">
                    <div className="w-32 h-32 rounded-full overflow-hidden border-4 border-purple-500/30 shadow-2xl">
                      {userAvatar ? (
                        <img
                          src={userAvatar}
                          alt="User Avatar"
                          className="w-full h-full object-cover"
                        />
                      ) : (
                        <div className="w-full h-full bg-gradient-to-br from-purple-500 to-blue-500 flex items-center justify-center text-white font-bold text-4xl">
                          {userName.charAt(0).toUpperCase()}
                        </div>
                      )}
                    </div>
                    <div className="absolute -bottom-2 -right-2 w-8 h-8 bg-green-500 rounded-full border-4 border-[#111111] flex items-center justify-center">
                      <CheckCircle className="w-4 h-4 text-white" />
                    </div>
                  </div>

                  <div className="flex-1 text-center lg:text-left">
                    <h2 className="text-3xl font-bold text-white mb-2">{userName}</h2>
                    <p className="text-purple-400 text-lg mb-4">{user.email}</p>
                    <div className="flex flex-wrap items-center justify-center lg:justify-start gap-4 mb-6">
                      <div className="flex items-center gap-2 bg-[#1a1a1a] px-3 py-1 rounded-full border border-gray-700/50">
                        <div className={`w-2 h-2 rounded-full ${
                          userStats?.accountStatus === 'active' ? 'bg-green-500 animate-pulse' :
                          userStats?.accountStatus === 'blocked' ? 'bg-red-500' : 'bg-yellow-500'
                        }`}></div>
                        <span className={`text-sm capitalize ${
                          userStats?.accountStatus === 'active' ? 'text-green-400' :
                          userStats?.accountStatus === 'blocked' ? 'text-red-400' : 'text-yellow-400'
                        }`}>
                          {userStats?.accountStatus || 'Loading...'}
                        </span>
                      </div>
                      <div className="flex items-center gap-2 bg-[#1a1a1a] px-3 py-1 rounded-full border border-gray-700/50">
                        <Calendar className="w-4 h-4 text-purple-400" />
                        <span className="text-gray-300 text-sm">Joined {userStats?.joinDate || 'Loading...'}</span>
                      </div>
                      <div className="flex items-center gap-2 bg-[#1a1a1a] px-3 py-1 rounded-full border border-gray-700/50">
                        <Clock className="w-4 h-4 text-blue-400" />
                        <span className="text-gray-300 text-sm">Last login {userStats?.lastLogin || 'Loading...'}</span>
                      </div>
                    </div>


                  </div>
                </div>
              </div>
            </motion.div>

            {/* Stats Cards */}
            <motion.div variants={itemVariants} className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-12">
              <motion.div variants={cardVariants}>
                <div className="bg-[#1a1a1a] border border-gray-700/50 rounded-xl p-6 text-center backdrop-blur-lg hover:border-purple-400/50 transition-all duration-300 hover:-translate-y-2">
                  <div className="w-12 h-12 flex items-center justify-center bg-gradient-to-br from-purple-900/30 to-purple-800/20 rounded-full mb-4 mx-auto">
                    <Activity className="h-6 w-6 text-purple-400" />
                  </div>
                  <h3 className="text-lg font-semibold mb-2 text-white">Total Operations</h3>
                  <p className="text-2xl font-bold text-purple-400">
                    {userStats?.totalOperations ?? <Loader2 className="w-6 h-6 animate-spin mx-auto" />}
                  </p>
                  <p className="text-gray-400 text-sm">Device operations</p>
                </div>
              </motion.div>

              <motion.div variants={cardVariants}>
                <div className="bg-[#1a1a1a] border border-gray-700/50 rounded-xl p-6 text-center backdrop-blur-lg hover:border-blue-400/50 transition-all duration-300 hover:-translate-y-2">
                  <div className="w-12 h-12 flex items-center justify-center bg-gradient-to-br from-blue-900/30 to-blue-800/20 rounded-full mb-4 mx-auto">
                    <Shield className="h-6 w-6 text-blue-400" />
                  </div>
                  <h3 className="text-lg font-semibold mb-2 text-white">Security Score</h3>
                  <p className="text-2xl font-bold text-blue-400">
                    {userStats?.securityScore ? `${userStats.securityScore}%` : <Loader2 className="w-6 h-6 animate-spin mx-auto" />}
                  </p>
                  <p className="text-gray-400 text-sm">Account security</p>
                </div>
              </motion.div>

              <motion.div variants={cardVariants}>
                <div className="bg-[#1a1a1a] border border-gray-700/50 rounded-xl p-6 text-center backdrop-blur-lg hover:border-yellow-400/50 transition-all duration-300 hover:-translate-y-2">
                  <div className="w-12 h-12 flex items-center justify-center bg-gradient-to-br from-yellow-900/30 to-yellow-800/20 rounded-full mb-4 mx-auto">
                    <Star className="h-6 w-6 text-yellow-400" />
                  </div>
                  <h3 className="text-lg font-semibold mb-2 text-white">User Rating</h3>
                  <p className="text-2xl font-bold text-yellow-400">
                    {userStats?.userRating ? userStats.userRating.toFixed(1) : <Loader2 className="w-6 h-6 animate-spin mx-auto" />}
                  </p>
                  <p className="text-gray-400 text-sm">Average rating</p>
                </div>
              </motion.div>
            </motion.div>

            {/* Tabs Navigation */}
            <motion.div variants={itemVariants} className="mb-8">
              <div className="bg-[#1a1a1a] border border-gray-700/50 rounded-xl p-2 backdrop-blur-lg">
                <div className="flex flex-wrap gap-2">
                  {tabs.map((tab) => {
                    const Icon = tab.icon;
                    return (
                      <button
                        key={tab.id}
                        onClick={() => setActiveTab(tab.id)}
                        className={`flex items-center gap-2 px-4 py-2 rounded-lg transition-all duration-300 ${
                          activeTab === tab.id
                            ? 'bg-purple-600 text-white shadow-lg'
                            : 'text-gray-400 hover:text-white hover:bg-gray-800/50'
                        }`}
                      >
                        <Icon className="w-4 h-4" />
                        <span className="font-medium">{tab.label}</span>
                      </button>
                    );
                  })}
                </div>
              </div>
            </motion.div>

            {/* Tab Content */}
            <AnimatePresence mode="wait">
              {activeTab === 'profile' && (
                <motion.div
                  key="profile"
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  exit={{ opacity: 0, y: -20 }}
                  transition={{ duration: 0.3 }}
                  className="max-w-2xl mx-auto"
                >
                  {/* Profile Information */}
                  <div className="bg-[#1a1a1a] border border-gray-700/50 rounded-xl p-8 backdrop-blur-lg">
                    <div className="flex items-center justify-between mb-6">
                      <h3 className="text-xl font-semibold text-white flex items-center gap-2">
                        <User className="w-5 h-5 text-purple-400" />
                        Profile Information
                      </h3>
                    </div>

                    <AnimatePresence>
                      {isEditing ? (
                        <motion.div
                          initial={{ opacity: 0, height: 0 }}
                          animate={{ opacity: 1, height: "auto" }}
                          exit={{ opacity: 0, height: 0 }}
                          className="space-y-6"
                        >
                          <div className="space-y-2">
                            <Label htmlFor="full_name" className="text-gray-300">
                              Full Name
                            </Label>
                            <Input
                              id="full_name"
                              value={formData.full_name}
                              onChange={(e) => setFormData({ ...formData, full_name: e.target.value })}
                              className="bg-gray-800 border-gray-600 text-white focus:border-purple-400"
                              placeholder="Enter your full name"
                            />
                          </div>

                          <div className="space-y-2">
                            <Label htmlFor="email" className="text-gray-300">
                              Email
                            </Label>
                            <Input
                              id="email"
                              value={formData.email}
                              disabled
                              className="bg-gray-800 border-gray-600 text-gray-400"
                            />
                          </div>

                          <div className="space-y-2">
                            <Label htmlFor="phone" className="text-gray-300">
                              Phone Number
                            </Label>
                            <Input
                              id="phone"
                              value={formData.phone}
                              onChange={(e) => setFormData({ ...formData, phone: e.target.value })}
                              className="bg-gray-800 border-gray-600 text-white focus:border-purple-400"
                              placeholder="Enter your phone number"
                            />
                          </div>

                          <div className="space-y-2">
                            <Label htmlFor="location" className="text-gray-300">
                              Location
                            </Label>
                            <Input
                              id="location"
                              value={formData.location}
                              disabled
                              className="bg-gray-800 border-gray-600 text-gray-400"
                              placeholder="Location (read-only)"
                            />
                          </div>

                          <div className="space-y-2">
                            <Label htmlFor="bio" className="text-gray-300">
                              Bio
                            </Label>
                            <textarea
                              id="bio"
                              value={formData.bio}
                              onChange={(e) => setFormData({ ...formData, bio: e.target.value })}
                              className="w-full bg-gray-800 border border-gray-600 text-white rounded-md px-3 py-2 focus:border-purple-400 focus:outline-none resize-none"
                              rows={3}
                              placeholder="Tell us about yourself"
                            />
                          </div>

                          <div className="flex gap-3 pt-4">
                            <Button
                              onClick={handleSave}
                              disabled={isLoading}
                              className="bg-purple-600 hover:bg-purple-700 text-white flex-1"
                            >
                              <Save className="w-4 h-4 mr-2" />
                              {isLoading ? "Saving..." : "Save Changes"}
                            </Button>
                            <Button
                              onClick={() => setIsEditing(false)}
                              variant="outline"
                              className="border-gray-600 text-gray-300 hover:bg-gray-800"
                            >
                              Cancel
                            </Button>
                          </div>
                        </motion.div>
                      ) : (
                        <motion.div
                          initial={{ opacity: 0 }}
                          animate={{ opacity: 1 }}
                          className="space-y-4"
                        >
                          <div className="flex items-center gap-3 p-3 bg-gray-800/50 rounded-lg">
                            <User className="w-5 h-5 text-purple-400" />
                            <div>
                              <p className="text-gray-400 text-sm">Full Name</p>
                              <p className="text-white font-medium">{formData.full_name || 'Not set'}</p>
                            </div>
                          </div>

                          <div className="flex items-center gap-3 p-3 bg-gray-800/50 rounded-lg">
                            <Mail className="w-5 h-5 text-blue-400" />
                            <div>
                              <p className="text-gray-400 text-sm">Email</p>
                              <p className="text-white font-medium">{formData.email}</p>
                            </div>
                          </div>

                          <div className="flex items-center gap-3 p-3 bg-gray-800/50 rounded-lg">
                            <Phone className="w-5 h-5 text-green-400" />
                            <div>
                              <p className="text-gray-400 text-sm">Phone</p>
                              <p className="text-white font-medium">{formData.phone || 'Not set'}</p>
                            </div>
                          </div>

                          <div className="flex items-center gap-3 p-3 bg-gray-800/50 rounded-lg">
                            <MapPin className="w-5 h-5 text-red-400" />
                            <div>
                              <p className="text-gray-400 text-sm">Location</p>
                              <p className="text-white font-medium">{formData.location || 'Not set'}</p>
                            </div>
                          </div>

                          {formData.bio && (
                            <div className="flex items-start gap-3 p-3 bg-gray-800/50 rounded-lg">
                              <Globe className="w-5 h-5 text-yellow-400 mt-1" />
                              <div>
                                <p className="text-gray-400 text-sm">Bio</p>
                                <p className="text-white font-medium">{formData.bio}</p>
                              </div>
                            </div>
                          )}

                          {profileData && (
                            <div className="flex items-center gap-3 p-3 bg-gray-800/50 rounded-lg">
                              <Award className="w-5 h-5 text-green-400" />
                              <div>
                                <p className="text-gray-400 text-sm">Credits</p>
                                <p className="text-white font-medium">{profileData.credits || '0'} credits</p>
                              </div>
                            </div>
                          )}

                          {/* Edit Profile Button at Bottom */}
                          <div className="pt-4 border-t border-gray-700/50">
                            <Button
                              onClick={() => setIsEditing(true)}
                              className="w-full bg-purple-600 hover:bg-purple-700 text-white transition-all duration-300 hover:scale-105"
                            >
                              <Edit3 className="w-4 h-4 mr-2" />
                              Edit Profile
                            </Button>
                          </div>
                        </motion.div>
                      )}
                    </AnimatePresence>
                  </div>
                </motion.div>
              )}

              {activeTab === 'settings' && (
                <motion.div
                  key="settings"
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  exit={{ opacity: 0, y: -20 }}
                  transition={{ duration: 0.3 }}
                  className="grid grid-cols-1 lg:grid-cols-2 gap-8"
                >
                  <div className="bg-[#1a1a1a] border border-gray-700/50 rounded-xl p-8 backdrop-blur-lg">
                    <h3 className="text-xl font-semibold text-white flex items-center gap-2 mb-6">
                      <Settings className="w-5 h-5 text-purple-400" />
                      General Settings
                    </h3>
                    <div className="space-y-4">
                      <div className="flex items-center justify-between p-4 bg-gray-800/50 rounded-lg">
                        <div>
                          <p className="text-white font-medium">Email Notifications</p>
                          <p className="text-gray-400 text-sm">Receive updates via email</p>
                        </div>
                        <div className="w-12 h-6 bg-purple-600 rounded-full relative cursor-pointer">
                          <div className="w-5 h-5 bg-white rounded-full absolute top-0.5 right-0.5 transition-all"></div>
                        </div>
                      </div>

                      <div className="flex items-center justify-between p-4 bg-gray-800/50 rounded-lg">
                        <div>
                          <p className="text-white font-medium">Push Notifications</p>
                          <p className="text-gray-400 text-sm">Get notified about important updates</p>
                        </div>
                        <div className="w-12 h-6 bg-gray-600 rounded-full relative cursor-pointer">
                          <div className="w-5 h-5 bg-white rounded-full absolute top-0.5 left-0.5 transition-all"></div>
                        </div>
                      </div>

                      <div className="flex items-center justify-between p-4 bg-gray-800/50 rounded-lg">
                        <div>
                          <p className="text-white font-medium">Dark Mode</p>
                          <p className="text-gray-400 text-sm">Use dark theme</p>
                        </div>
                        <div className="w-12 h-6 bg-purple-600 rounded-full relative cursor-pointer">
                          <div className="w-5 h-5 bg-white rounded-full absolute top-0.5 right-0.5 transition-all"></div>
                        </div>
                      </div>
                    </div>
                  </div>

                  <div className="bg-[#1a1a1a] border border-gray-700/50 rounded-xl p-8 backdrop-blur-lg">
                    <h3 className="text-xl font-semibold text-white flex items-center gap-2 mb-6">
                      <Palette className="w-5 h-5 text-purple-400" />
                      Appearance
                    </h3>
                    <div className="space-y-4">
                      <div className="p-4 bg-gray-800/50 rounded-lg">
                        <p className="text-white font-medium mb-3">Theme Color</p>
                        <div className="flex gap-3">
                          <div className="w-8 h-8 bg-purple-500 rounded-full cursor-pointer ring-2 ring-purple-400 ring-offset-2 ring-offset-[#1a1a1a]"></div>
                          <div className="w-8 h-8 bg-blue-500 rounded-full cursor-pointer"></div>
                          <div className="w-8 h-8 bg-green-500 rounded-full cursor-pointer"></div>
                          <div className="w-8 h-8 bg-red-500 rounded-full cursor-pointer"></div>
                        </div>
                      </div>
                    </div>
                  </div>
                </motion.div>
              )}

              {activeTab === 'security' && (
                <motion.div
                  key="security"
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  exit={{ opacity: 0, y: -20 }}
                  transition={{ duration: 0.3 }}
                  className="grid grid-cols-1 lg:grid-cols-2 gap-8"
                >
                  <div className="bg-[#1a1a1a] border border-gray-700/50 rounded-xl p-8 backdrop-blur-lg">
                    <h3 className="text-xl font-semibold text-white flex items-center gap-2 mb-6">
                      <Shield className="w-5 h-5 text-purple-400" />
                      Security Settings
                    </h3>
                    <div className="space-y-4">
                      <div className="p-4 bg-gray-800/50 rounded-lg">
                        <div className="flex items-center justify-between mb-2">
                          <p className="text-white font-medium">Password</p>
                          <Button size="sm" className="bg-purple-600 hover:bg-purple-700">
                            Change
                          </Button>
                        </div>
                        <p className="text-gray-400 text-sm">Last changed 30 days ago</p>
                      </div>

                      {/* Two-Factor Authentication */}
                      <div className="p-4 bg-gray-800/50 rounded-lg">
                        <div className="flex items-center justify-between mb-2">
                          <p className="text-white font-medium">Two-Factor Authentication</p>
                          {twoFactorEnabled ? (
                            <Button
                              size="sm"
                              variant="outline"
                              className="border-red-600 text-red-400 hover:bg-red-600/10"
                              onClick={disable2FA}
                              disabled={isLoading}
                            >
                              Disable
                            </Button>
                          ) : (
                            <Button
                              size="sm"
                              className="bg-green-600 hover:bg-green-700"
                              onClick={generateTwoFactorSecret}
                              disabled={isLoading}
                            >
                              Enable
                            </Button>
                          )}
                        </div>
                        <p className="text-gray-400 text-sm">
                          {twoFactorEnabled ? "2FA is currently enabled" : "Add an extra layer of security"}
                        </p>

                        {/* QR Code Display */}
                        {showQrCode && (
                          <div className="mt-4 p-4 bg-gray-900/50 rounded-lg">
                            <p className="text-white font-medium mb-3">Scan QR Code with your authenticator app:</p>
                            {qrCodeUrl && (
                              <div className="flex flex-col items-center gap-4">
                                <img src={qrCodeUrl} alt="2FA QR Code" className="w-48 h-48 bg-white p-2 rounded-lg" />
                                <div className="w-full">
                                  <Label htmlFor="verification_code" className="text-gray-300 text-sm">
                                    Enter verification code:
                                  </Label>
                                  <div className="flex gap-2 mt-2">
                                    <Input
                                      id="verification_code"
                                      value={verificationCode}
                                      onChange={(e) => setVerificationCode(e.target.value)}
                                      className="bg-gray-800 border-gray-600 text-white focus:border-purple-400"
                                      placeholder="000000"
                                      maxLength={6}
                                    />
                                    <Button
                                      onClick={verifyAndEnable2FA}
                                      disabled={isLoading || !verificationCode}
                                      className="bg-purple-600 hover:bg-purple-700"
                                    >
                                      Verify
                                    </Button>
                                  </div>
                                </div>
                              </div>
                            )}
                          </div>
                        )}
                      </div>

                      {/* Reset HWID PC */}
                      <div className="p-4 bg-gray-800/50 rounded-lg">
                        <div className="flex items-center justify-between mb-2">
                          <p className="text-white font-medium">Reset HWID PC</p>
                          <Button
                            size="sm"
                            variant="outline"
                            className="border-orange-600 text-orange-400 hover:bg-orange-600/10"
                            onClick={() => setShowHwidResetDialog(true)}
                            disabled={hwidResetLoading}
                          >
                            <RefreshCw className="w-4 h-4 mr-2" />
                            Reset HWID
                          </Button>
                        </div>
                        <p className="text-gray-400 text-sm">Reset your hardware ID (deducts 15 days from expiration)</p>
                      </div>
                    </div>
                  </div>

                  <div className="bg-[#1a1a1a] border border-gray-700/50 rounded-xl p-8 backdrop-blur-lg">
                    <h3 className="text-xl font-semibold text-white flex items-center gap-2 mb-6">
                      <Key className="w-5 h-5 text-purple-400" />
                      Active Sessions
                    </h3>
                    {sessionsLoading ? (
                      <div className="flex items-center justify-center py-8">
                        <Loader2 className="w-6 h-6 animate-spin text-purple-400" />
                      </div>
                    ) : (
                      <div className="space-y-4">
                        {userSessions.map((session) => (
                          <div key={session.id} className="p-4 bg-gray-800/50 rounded-lg">
                            <div className="flex items-center justify-between mb-2">
                              <div className="flex items-center gap-3">
                                <div className="w-10 h-10 bg-blue-500/20 rounded-full flex items-center justify-center">
                                  {session.device.includes('Mobile') || session.device.includes('iPhone') ? (
                                    <Smartphone className="w-5 h-5 text-blue-400" />
                                  ) : (
                                    <Monitor className="w-5 h-5 text-blue-400" />
                                  )}
                                </div>
                                <div>
                                  <p className="text-white font-medium">{session.isCurrent ? 'Current Session' : session.device}</p>
                                  <p className="text-gray-400 text-sm">{session.device} • {session.browser}</p>
                                </div>
                              </div>
                              {session.isCurrent ? (
                                <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                              ) : (
                                <Button
                                  size="sm"
                                  variant="outline"
                                  className="border-red-600 text-red-400 hover:bg-red-600/10"
                                  onClick={() => revokeSession(session.id)}
                                >
                                  <Trash2 className="w-4 h-4 mr-1" />
                                  Revoke
                                </Button>
                              )}
                            </div>
                            <p className="text-gray-400 text-xs">Last active: {session.lastActive}</p>
                          </div>
                        ))}
                      </div>
                    )}
                  </div>
                </motion.div>
              )}

              {activeTab === 'activity' && (
                <motion.div
                  key="activity"
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  exit={{ opacity: 0, y: -20 }}
                  transition={{ duration: 0.3 }}
                  className="bg-[#1a1a1a] border border-gray-700/50 rounded-xl p-8 backdrop-blur-lg"
                >
                  <h3 className="text-xl font-semibold text-white flex items-center gap-2 mb-6">
                    <Activity className="w-5 h-5 text-purple-400" />
                    Recent Activity
                  </h3>

                  <div className="space-y-4">
                    {getPaginatedOperations().length > 0 ? (
                      getPaginatedOperations().map((operation: OperationDetail, index: number) => {
                        const getActivityIcon = (operationType: string) => {
                          switch (operationType.toLowerCase()) {
                            case 'direct unlock':
                              return { icon: Key, color: 'text-green-400' };
                            case 'remove frp':
                            case 'remove frp [brom]':
                            case 'remove frp [dm]':
                              return { icon: Shield, color: 'text-blue-400' };
                            case 'write cert':
                              return { icon: Award, color: 'text-purple-400' };
                            case 'convert csc':
                              return { icon: Settings, color: 'text-yellow-400' };
                            default:
                              return { icon: Activity, color: 'text-gray-400' };
                          }
                        };

                        const { icon: Icon, color } = getActivityIcon(operation.operation_type);

                        return (
                          <motion.div
                            key={operation.operation_id}
                            initial={{ opacity: 0, x: -20 }}
                            animate={{ opacity: 1, x: 0 }}
                            transition={{ delay: index * 0.1 }}
                            className="flex items-center gap-4 p-4 bg-gray-800/50 rounded-lg hover:bg-gray-800/70 transition-colors"
                          >
                            <div className={`w-10 h-10 rounded-full bg-gray-700 flex items-center justify-center`}>
                              <Icon className={`w-5 h-5 ${color}`} />
                            </div>
                            <div className="flex-1">
                              <p className="text-white font-medium">{operation.operation_type}</p>
                              <div className="flex items-center gap-2 text-sm text-gray-400">
                                <span>{operation.time}</span>
                                {operation.model && (
                                  <>
                                    <span>•</span>
                                    <span>{operation.model}</span>
                                  </>
                                )}
                                {operation.status && (
                                  <>
                                    <span>•</span>
                                    <span className={`${
                                      operation.status.toLowerCase() === 'success' ? 'text-green-400' :
                                      operation.status.toLowerCase() === 'failed' ? 'text-red-400' : 'text-yellow-400'
                                    }`}>
                                      {operation.status}
                                    </span>
                                  </>
                                )}
                              </div>
                            </div>
                            <Button
                              size="sm"
                              variant="outline"
                              className="border-purple-600 text-purple-400 hover:bg-purple-600/10"
                              onClick={() => {
                                setSelectedOperation(operation);
                                setShowOperationDetails(true);
                              }}
                            >
                              <Eye className="w-4 h-4 mr-1" />
                              View Details
                            </Button>
                          </motion.div>
                        );
                      })
                    ) : (
                      <div className="text-center py-8">
                        <Activity className="w-12 h-12 text-gray-600 mx-auto mb-4" />
                        <p className="text-gray-400">No recent activity</p>
                        <p className="text-gray-500 text-sm">Your operations will appear here</p>
                      </div>
                    )}
                  </div>

                  {/* Pagination Controls */}
                  {getTotalPages() > 1 && (
                    <div className="flex items-center justify-between mt-6 pt-6 border-t border-gray-700/50">
                      <div className="text-sm text-gray-400">
                        Showing {((currentPage - 1) * 5) + 1} to {Math.min(currentPage * 5, totalOperations)} of {totalOperations} operations
                      </div>
                      <div className="flex items-center gap-2">
                        <Button
                          size="sm"
                          variant="outline"
                          className="border-gray-600 text-gray-300 hover:bg-gray-800"
                          onClick={() => setCurrentPage(prev => Math.max(1, prev - 1))}
                          disabled={currentPage === 1}
                        >
                          <ChevronLeft className="w-4 h-4" />
                          Previous
                        </Button>
                        <span className="text-sm text-gray-400 px-3">
                          Page {currentPage} of {getTotalPages()}
                        </span>
                        <Button
                          size="sm"
                          variant="outline"
                          className="border-gray-600 text-gray-300 hover:bg-gray-800"
                          onClick={() => setCurrentPage(prev => Math.min(getTotalPages(), prev + 1))}
                          disabled={currentPage === getTotalPages()}
                        >
                          Next
                          <ChevronRight className="w-4 h-4" />
                        </Button>
                      </div>
                    </div>
                  )}
                </motion.div>
              )}
            </AnimatePresence>
          </motion.div>
        </div>

        {/* HWID Reset Warning Dialog */}
        {showHwidResetDialog && (
          <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4">
            <motion.div
              initial={{ opacity: 0, scale: 0.95 }}
              animate={{ opacity: 1, scale: 1 }}
              className="bg-[#1a1a1a] border border-gray-700/50 rounded-xl p-6 max-w-md w-full"
            >
              <div className="flex items-center gap-3 mb-4">
                <div className="w-10 h-10 bg-orange-500/20 rounded-full flex items-center justify-center">
                  <AlertCircle className="w-5 h-5 text-orange-400" />
                </div>
                <h3 className="text-xl font-semibold text-white">Reset HWID Warning</h3>
              </div>

              <div className="mb-6">
                <p className="text-gray-300 mb-3">
                  Are you sure you want to reset your Hardware ID (HWID)?
                </p>
                <div className="bg-orange-500/10 border border-orange-500/30 rounded-lg p-3">
                  <p className="text-orange-400 text-sm font-medium">
                    ⚠️ This action will deduct 15 days from your account expiration date.
                  </p>
                </div>
              </div>

              <div className="flex gap-3">
                <Button
                  onClick={() => setShowHwidResetDialog(false)}
                  variant="outline"
                  className="flex-1 border-gray-600 text-gray-300 hover:bg-gray-800"
                  disabled={hwidResetLoading}
                >
                  Cancel
                </Button>
                <Button
                  onClick={resetHwid}
                  className="flex-1 bg-orange-600 hover:bg-orange-700 text-white"
                  disabled={hwidResetLoading}
                >
                  {hwidResetLoading ? (
                    <>
                      <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                      Resetting...
                    </>
                  ) : (
                    <>
                      <RefreshCw className="w-4 h-4 mr-2" />
                      Reset HWID
                    </>
                  )}
                </Button>
              </div>
            </motion.div>
          </div>
        )}

        {/* Operation Details Dialog */}
        {showOperationDetails && selectedOperation && (
          <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4">
            <motion.div
              initial={{ opacity: 0, scale: 0.95 }}
              animate={{ opacity: 1, scale: 1 }}
              className="bg-[#1a1a1a] border border-gray-700/50 rounded-xl p-6 max-w-2xl w-full max-h-[80vh] overflow-y-auto"
            >
              <div className="flex items-center justify-between mb-6">
                <h3 className="text-xl font-semibold text-white">Operation Details</h3>
                <Button
                  onClick={() => setShowOperationDetails(false)}
                  variant="ghost"
                  size="sm"
                  className="text-gray-400 hover:text-white"
                >
                  ✕
                </Button>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-3">
                  <div className="p-3 bg-gray-800/50 rounded-lg">
                    <p className="text-gray-400 text-sm">Operation Type</p>
                    <p className="text-white font-medium">{selectedOperation.operation_type}</p>
                  </div>

                  <div className="p-3 bg-gray-800/50 rounded-lg">
                    <p className="text-gray-400 text-sm">Status</p>
                    <p className={`font-medium ${
                      selectedOperation.status?.toLowerCase() === 'success' ? 'text-green-400' :
                      selectedOperation.status?.toLowerCase() === 'failed' ? 'text-red-400' : 'text-yellow-400'
                    }`}>
                      {selectedOperation.status || 'Unknown'}
                    </p>
                  </div>

                  <div className="p-3 bg-gray-800/50 rounded-lg">
                    <p className="text-gray-400 text-sm">Time</p>
                    <p className="text-white font-medium">{selectedOperation.time}</p>
                  </div>

                  {selectedOperation.model && (
                    <div className="p-3 bg-gray-800/50 rounded-lg">
                      <p className="text-gray-400 text-sm">Model</p>
                      <p className="text-white font-medium">{selectedOperation.model}</p>
                    </div>
                  )}

                  {selectedOperation.brand && (
                    <div className="p-3 bg-gray-800/50 rounded-lg">
                      <p className="text-gray-400 text-sm">Brand</p>
                      <p className="text-white font-medium">{selectedOperation.brand}</p>
                    </div>
                  )}
                </div>

                <div className="space-y-3">
                  {selectedOperation.imei && (
                    <div className="p-3 bg-gray-800/50 rounded-lg">
                      <p className="text-gray-400 text-sm">IMEI</p>
                      <p className="text-white font-medium">{selectedOperation.imei}</p>
                    </div>
                  )}

                  {selectedOperation.android && (
                    <div className="p-3 bg-gray-800/50 rounded-lg">
                      <p className="text-gray-400 text-sm">Android Version</p>
                      <p className="text-white font-medium">{selectedOperation.android}</p>
                    </div>
                  )}

                  {selectedOperation.baseband && (
                    <div className="p-3 bg-gray-800/50 rounded-lg">
                      <p className="text-gray-400 text-sm">Baseband</p>
                      <p className="text-white font-medium">{selectedOperation.baseband}</p>
                    </div>
                  )}

                  {selectedOperation.carrier && (
                    <div className="p-3 bg-gray-800/50 rounded-lg">
                      <p className="text-gray-400 text-sm">Carrier</p>
                      <p className="text-white font-medium">{selectedOperation.carrier}</p>
                    </div>
                  )}

                  {selectedOperation.credit && (
                    <div className="p-3 bg-gray-800/50 rounded-lg">
                      <p className="text-gray-400 text-sm">Credits Used</p>
                      <p className="text-white font-medium">{selectedOperation.credit}</p>
                    </div>
                  )}
                </div>
              </div>

              <div className="mt-6 pt-4 border-t border-gray-700/50">
                <Button
                  onClick={() => setShowOperationDetails(false)}
                  className="w-full bg-purple-600 hover:bg-purple-700 text-white"
                >
                  Close
                </Button>
              </div>
            </motion.div>
          </div>
        )}
      </div>
    </ProtectedRoute>
  );
};

export default NewUserProfilePage;
